#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试查询结算信息确认功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'trader'))

from trader.ctp_direct_wrapper import CTPDirectWrapper

def test_settlement_info_confirm():
    """测试查询结算信息确认功能"""
    print("=== 测试查询结算信息确认功能 ===")
    
    # 创建CTP服务实例
    ctp_service = CTPDirectWrapper()
    
    try:
        # 初始化服务
        print("1. 初始化CTP服务...")
        if not ctp_service.initialize():
            print("初始化失败")
            return False
        
        print("2. 等待连接和登录...")
        # 这里需要等待连接和登录完成
        # 在实际环境中，需要等待OnRspUserLogin回调
        
        print("3. 查询结算信息确认...")
        result = ctp_service.query_settlement_info_confirm()
        
        if result["success"]:
            print(f"查询成功，返回 {result['count']} 条记录")
            if result["data"]:
                print("结算信息确认详情:")
                for i, settlement_confirm in enumerate(result["data"]):
                    print(f"  记录 {i+1}:")
                    print(f"    经纪公司代码: {settlement_confirm.get('BrokerID', '')}")
                    print(f"    投资者代码: {settlement_confirm.get('InvestorID', '')}")
                    print(f"    确认日期: {settlement_confirm.get('ConfirmDate', '')}")
                    print(f"    确认时间: {settlement_confirm.get('ConfirmTime', '')}")
                    print(f"    结算编号: {settlement_confirm.get('SettlementID', '')}")
                    print(f"    投资者帐号: {settlement_confirm.get('AccountID', '')}")
                    print(f"    币种代码: {settlement_confirm.get('CurrencyID', '')}")
            else:
                print("没有找到结算信息确认记录")
        else:
            print(f"查询失败: {result.get('error_msg', '未知错误')}")
            
        return result["success"]
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        return False

def main():
    """主函数"""
    print("开始测试查询结算信息确认功能...")
    
    success = test_settlement_info_confirm()
    
    if success:
        print("\n✓ 测试通过")
    else:
        print("\n✗ 测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

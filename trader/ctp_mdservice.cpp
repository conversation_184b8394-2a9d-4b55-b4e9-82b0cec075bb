#include "ctp_mdservice.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <cstring>
#include <algorithm>
#include <limits>

CTPMdService::CTPMdService()
    : m_api(nullptr), m_request_id(0),
      m_connected(false), m_logged_in(false), m_response_ready(false),
      m_market_data_callback(nullptr), m_subscribe_callback(nullptr) {
}

CTPMdService::~CTPMdService() {
    if (m_api) {
        m_api->Release();
    }
}

bool CTPMdService::initialize(const CTPConnectionConfig& config) {
    // 保存配置
    m_config = config;

    // 创建行情API实例
    m_api = CThostFtdcMdApi::CreateFtdcMdApi(m_config.md_flow_dir);

    // 注册回调
    m_api->RegisterSpi(this);

    // 连接前置机
    m_api->RegisterFront(const_cast<char*>(m_config.md_front));
    m_api->Init();

    return true;
}

void CTPMdService::setMarketDataCallback(MarketDataCallback callback) {
    m_market_data_callback = callback;
}

void CTPMdService::setSubscribeCallback(SubscribeCallback callback) {
    m_subscribe_callback = callback;
}

void CTPMdService::setResponse(bool success, const std::string& error_msg) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_current_response.success = success;
    m_current_response.error_msg = error_msg;
    m_response_ready = true;
    m_cv.notify_all();
}

void CTPMdService::resetResponseState() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_response_ready = false;
}

// 回调函数实现
void CTPMdService::OnFrontConnected() {
    std::cout << "行情前置连接成功，开始登录..." << std::endl;
    m_connected = true;
    reqUserLogin();
}

void CTPMdService::OnFrontDisconnected(int nReason) {
    m_connected = false;
    m_logged_in = false;
    std::cout << "行情前置连接断开，原因: " << nReason << std::endl;
}

void CTPMdService::reqUserLogin() {
    CThostFtdcReqUserLoginField req;
    memset(&req, 0, sizeof(req));
    strcpy(req.BrokerID, m_config.md_broker_id);
    strcpy(req.UserID, m_config.md_user_id);
    strcpy(req.Password, m_config.md_password);

    int rt = m_api->ReqUserLogin(&req, ++m_request_id);
    if (rt != 0) {
        std::cout << "发送行情登录请求失败，错误码: " << rt << std::endl;
    } else {
        std::cout << "发送行情登录请求成功" << std::endl;
    }
}

void CTPMdService::OnRspUserLogin(CThostFtdcRspUserLoginField *pRspUserLogin, 
                                 CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (!isErrorRspInfo(pRspInfo)) {
        std::cout << "=====行情登录成功=====" << std::endl;
        std::cout << "交易日： " << pRspUserLogin->TradingDay << std::endl;
        std::cout << "登录时间： " << pRspUserLogin->LoginTime << std::endl;
        
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_logged_in = true;
        }
        m_cv.notify_all();
    } else {
        std::cerr << "行情登录失败--->>> ErrorID=" << pRspInfo->ErrorID 
                  << ", ErrorMsg=" << pRspInfo->ErrorMsg << std::endl;
    }
}

void CTPMdService::OnRspSubMarketData(CThostFtdcSpecificInstrumentField *pSpecificInstrument, 
                                     CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (!isErrorRspInfo(pRspInfo)) {
        std::cout << "订阅行情成功: " << pSpecificInstrument->InstrumentID << std::endl;
        
        // 添加到已订阅列表
        std::string instrument_id = pSpecificInstrument->InstrumentID;
        auto it = std::find(m_subscribed_instruments.begin(), m_subscribed_instruments.end(), instrument_id);
        if (it == m_subscribed_instruments.end()) {
            m_subscribed_instruments.push_back(instrument_id);
        }
        
        // 调用回调
        if (m_subscribe_callback) {
            m_subscribe_callback(pSpecificInstrument->InstrumentID, true, "");
        }
    } else {
        std::cerr << "订阅行情失败: " << pSpecificInstrument->InstrumentID 
                  << " ErrorID=" << pRspInfo->ErrorID 
                  << ", ErrorMsg=" << pRspInfo->ErrorMsg << std::endl;
        
        // 调用回调
        if (m_subscribe_callback) {
            m_subscribe_callback(pSpecificInstrument->InstrumentID, false, pRspInfo->ErrorMsg);
        }
    }
    
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo), 
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPMdService::OnRspUnSubMarketData(CThostFtdcSpecificInstrumentField *pSpecificInstrument, 
                                       CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (!isErrorRspInfo(pRspInfo)) {
        std::cout << "取消订阅行情成功: " << pSpecificInstrument->InstrumentID << std::endl;
        
        // 从已订阅列表中移除
        std::string instrument_id = pSpecificInstrument->InstrumentID;
        auto it = std::find(m_subscribed_instruments.begin(), m_subscribed_instruments.end(), instrument_id);
        if (it != m_subscribed_instruments.end()) {
            m_subscribed_instruments.erase(it);
        }
        
        // 从数据缓存中移除
        m_market_data.erase(instrument_id);
    } else {
        std::cerr << "取消订阅行情失败: " << pSpecificInstrument->InstrumentID 
                  << " ErrorID=" << pRspInfo->ErrorID 
                  << ", ErrorMsg=" << pRspInfo->ErrorMsg << std::endl;
    }
    
    if (bIsLast) {
        setResponse(!isErrorRspInfo(pRspInfo), 
                   pRspInfo ? pRspInfo->ErrorMsg : "");
    }
}

void CTPMdService::OnRtnDepthMarketData(CThostFtdcDepthMarketDataField *pDepthMarketData) {
    if (!pDepthMarketData) return;
    
    std::string instrument_id = pDepthMarketData->InstrumentID;
    
    // 更新缓存数据
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_market_data[instrument_id] = *pDepthMarketData;
    }
    
    // 输出行情信息
    std::cout << "收到行情数据 - 合约: " << pDepthMarketData->InstrumentID 
              << ", 最新价: " << pDepthMarketData->LastPrice
              << ", 买一价: " << pDepthMarketData->BidPrice1
              << ", 卖一价: " << pDepthMarketData->AskPrice1
              << ", 成交量: " << pDepthMarketData->Volume
              << ", 更新时间: " << pDepthMarketData->UpdateTime << std::endl;
    
    // 处理无效价格
    if (pDepthMarketData->AskPrice5 == std::numeric_limits<double>::max()) {
        const_cast<CThostFtdcDepthMarketDataField*>(pDepthMarketData)->AskPrice5 = 0;
    }
    
    // 调用用户回调
    if (m_market_data_callback) {
        m_market_data_callback(pDepthMarketData);
    }
}

void CTPMdService::OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) {
    if (pRspInfo) {
        std::cerr << "收到行情错误响应--->>> ErrorID=" << pRspInfo->ErrorID 
                  << ", ErrorMsg=" << pRspInfo->ErrorMsg << std::endl;
    }
}

bool CTPMdService::isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo) {
    return pRspInfo && pRspInfo->ErrorID != 0;
}

void CTPMdService::waitForResponse(int timeout_seconds) {
    std::unique_lock<std::mutex> lock(m_mutex);
    m_cv.wait_for(lock, std::chrono::seconds(timeout_seconds), 
                  [this] { return m_response_ready; });
}

bool CTPMdService::waitForLogin(int timeout_seconds) {
    std::unique_lock<std::mutex> lock(m_mutex);
    return m_cv.wait_for(lock, std::chrono::seconds(timeout_seconds), 
                        [this] { return m_logged_in; });
}

bool CTPMdService::subscribeMarketData(const std::vector<std::string>& instruments) {
    if (!m_logged_in) {
        std::cout << "未登录，无法订阅行情" << std::endl;
        return false;
    }
    
    // 转换为char*数组
    std::vector<char*> instrument_ids;
    for (const auto& inst : instruments) {
        instrument_ids.push_back(const_cast<char*>(inst.c_str()));
    }
    
    int rt = m_api->SubscribeMarketData(instrument_ids.data(), instrument_ids.size());
    if (rt != 0) {
        std::cout << "订阅行情请求发送失败，错误码: " << rt << std::endl;
        return false;
    }
    
    std::cout << "订阅行情请求已发送，合约数量: " << instruments.size() << std::endl;
    return true;
}

bool CTPMdService::unsubscribeMarketData(const std::vector<std::string>& instruments) {
    if (!m_logged_in) {
        std::cout << "未登录，无法取消订阅行情" << std::endl;
        return false;
    }
    
    // 转换为char*数组
    std::vector<char*> instrument_ids;
    for (const auto& inst : instruments) {
        instrument_ids.push_back(const_cast<char*>(inst.c_str()));
    }
    
    int rt = m_api->UnSubscribeMarketData(instrument_ids.data(), instrument_ids.size());
    if (rt != 0) {
        std::cout << "取消订阅行情请求发送失败，错误码: " << rt << std::endl;
        return false;
    }
    
    std::cout << "取消订阅行情请求已发送，合约数量: " << instruments.size() << std::endl;
    return true;
}

bool CTPMdService::subscribeMarketDataComplete(const std::vector<std::string>& instruments) {
    // 重置响应状态
    resetResponseState();
    
    // 发送订阅请求
    if (!subscribeMarketData(instruments)) {
        return false;
    }
    
    // 等待响应
    waitForResponse();
    
    // 返回操作结果
    return m_current_response.success;
}

bool CTPMdService::getLatestMarketData(const std::string& instrument_id, 
                                      CThostFtdcDepthMarketDataField& data) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto it = m_market_data.find(instrument_id);
    if (it != m_market_data.end()) {
        data = it->second;
        return true;
    }
    return false;
}

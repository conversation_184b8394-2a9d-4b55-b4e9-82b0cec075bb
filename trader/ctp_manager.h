#ifndef CTP_MANAGER_H
#define CTP_MANAGER_H

#include "ctp_webservice.h"
#include "ctp_mdservice.h"
#include "ctp_config.h"
#include <memory>
#include <string>
#include <vector>

// CTP统一管理器
class CTPManager {
public:
    CTPManager();
    ~CTPManager();
    
    // 初始化
    bool initialize(const CTPConnectionConfig& config);
    bool initializeFromConfigFile(const std::string& env_config_path = "config/env_config.yaml");
    
    // 启动服务
    bool startTradeService();
    bool startMdService();
    bool startAllServices();
    
    // 停止服务
    void stopTradeService();
    void stopMdService();
    void stopAllServices();
    
    // 状态查询
    bool isTradeReady() const;
    bool isMdReady() const;
    bool isAllReady() const;
    
    // 获取服务实例
    CTPWebService* getTradeService() { return m_trade_service.get(); }
    CTPMdService* getMdService() { return m_md_service.get(); }
    
    // 便捷接口 - 交易相关
    bool placeOrder(const std::string& instrument_id, double price, int volume, 
                   const std::string& direction, const std::string& offset_flag, 
                   const std::string& exchange_id = "");
    bool cancelOrder(const std::string& order_ref, const std::string& instrument_id, 
                    const std::string& exchange_id = "");
    
    // 便捷接口 - 查询相关
    bool queryAccount(std::vector<CThostFtdcTradingAccountField>& results);
    bool queryPosition(const std::string& instrument_id, std::vector<CThostFtdcInvestorPositionField>& results);
    bool queryInstrument(const std::string& instrument_id, const std::string& exchange_id, 
                        std::vector<CThostFtdcInstrumentField>& results);
    
    // 便捷接口 - 行情相关
    bool subscribeMarketData(const std::vector<std::string>& instruments);
    bool unsubscribeMarketData(const std::vector<std::string>& instruments);
    bool getLatestMarketData(const std::string& instrument_id, CThostFtdcDepthMarketDataField& data);
    std::vector<std::string> getSubscribedInstruments();
    
    // 回调设置
    void setOrderCallback(CTPWebService::OrderCallbackDirect callback);
    void setTradeCallback(CTPWebService::TradeCallbackDirect callback);
    void setMarketDataCallback(CTPMdService::MarketDataCallback callback);
    void setSubscribeCallback(CTPMdService::SubscribeCallback callback);
    
    // 等待服务就绪
    bool waitForTradeReady(int timeout_seconds = 30);
    bool waitForMdReady(int timeout_seconds = 30);
    bool waitForAllReady(int timeout_seconds = 60);

private:
    std::unique_ptr<CTPWebService> m_trade_service;
    std::unique_ptr<CTPMdService> m_md_service;
    CTPConnectionConfig m_config;
    std::vector<std::string> m_default_instruments;
    bool m_initialized;

    // 内部辅助方法
    bool loadConfigFromFile(const std::string& env_config_path);
    bool loadDefaultConfig();
    void parseDefaultInstruments();
    void setConfigValue(const std::string& key, const std::string& value);
    void setupDefaultCallbacks();
};

#endif // CTP_MANAGER_H

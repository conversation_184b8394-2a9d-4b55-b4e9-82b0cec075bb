#include "ctp_manager.h"
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>
#include <sstream>

// 如果有yaml配置文件支持，可以包含yaml库
// #include <yaml-cpp/yaml.h>

CTPManager::CTPManager() : m_initialized(false) {
    m_trade_service = std::make_unique<CTPWebService>();
    m_md_service = std::make_unique<CTPMdService>();
}

CTPManager::~CTPManager() {
    stopAllServices();
}

bool CTPManager::initialize(const CTPConnectionConfig& config) {
    m_config = config;
    parseDefaultInstruments();
    m_initialized = true;

    std::cout << "CTP管理器初始化完成" << std::endl;
    return true;
}

bool CTPManager::initializeFromConfigFile(const std::string& env_config_path) {
    if (!loadConfigFromFile(env_config_path)) {
        std::cerr << "加载配置文件失败" << std::endl;
        return false;
    }

    return initialize(m_config);
}

bool CTPManager::startTradeService() {
    if (!m_initialized) {
        std::cerr << "管理器未初始化" << std::endl;
        return false;
    }
    
    std::cout << "启动交易服务..." << std::endl;
    return m_trade_service->initialize(m_config);
}

bool CTPManager::startMdService() {
    if (!m_initialized) {
        std::cerr << "管理器未初始化" << std::endl;
        return false;
    }
    
    std::cout << "启动行情服务..." << std::endl;
    return m_md_service->initialize(m_config);
}

bool CTPManager::startAllServices() {
    std::cout << "启动所有CTP服务..." << std::endl;
    
    // 先启动交易服务
    if (!startTradeService()) {
        std::cerr << "交易服务启动失败" << std::endl;
        return false;
    }
    
    // 等待交易服务就绪
    if (!waitForTradeReady()) {
        std::cerr << "交易服务就绪超时" << std::endl;
        return false;
    }
    
    // 启动行情服务
    if (!startMdService()) {
        std::cerr << "行情服务启动失败" << std::endl;
        return false;
    }
    
    // 等待行情服务就绪
    if (!waitForMdReady()) {
        std::cerr << "行情服务就绪超时" << std::endl;
        return false;
    }
    
    // 订阅默认合约
    if (!m_default_instruments.empty()) {
        std::cout << "订阅默认合约..." << std::endl;
        subscribeMarketData(m_default_instruments);
    }
    
    std::cout << "所有CTP服务启动完成" << std::endl;
    return true;
}

void CTPManager::stopTradeService() {
    // CTPWebService的析构函数会自动释放资源
    std::cout << "交易服务已停止" << std::endl;
}

void CTPManager::stopMdService() {
    // CTPMdService的析构函数会自动释放资源
    std::cout << "行情服务已停止" << std::endl;
}

void CTPManager::stopAllServices() {
    stopTradeService();
    stopMdService();
    std::cout << "所有CTP服务已停止" << std::endl;
}

bool CTPManager::isTradeReady() const {
    return m_trade_service && m_trade_service->isLoggedIn();
}

bool CTPManager::isMdReady() const {
    return m_md_service && m_md_service->isLoggedIn();
}

bool CTPManager::isAllReady() const {
    return isTradeReady() && isMdReady();
}

bool CTPManager::waitForTradeReady(int timeout_seconds) {
    auto start_time = std::chrono::steady_clock::now();
    while (!isTradeReady()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time).count();
        if (elapsed >= timeout_seconds) {
            return false;
        }
    }
    return true;
}

bool CTPManager::waitForMdReady(int timeout_seconds) {
    return m_md_service->waitForLogin(timeout_seconds);
}

bool CTPManager::waitForAllReady(int timeout_seconds) {
    int half_timeout = timeout_seconds / 2;
    return waitForTradeReady(half_timeout) && waitForMdReady(half_timeout);
}

// 便捷接口实现
bool CTPManager::placeOrder(const std::string& instrument_id, double price, int volume, 
                           const std::string& direction, const std::string& offset_flag, 
                           const std::string& exchange_id) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    char dir = (direction == "buy" || direction == "BUY") ? THOST_FTDC_D_Buy : THOST_FTDC_D_Sell;
    char offset = THOST_FTDC_OF_Open;
    if (offset_flag == "close" || offset_flag == "CLOSE") {
        offset = THOST_FTDC_OF_Close;
    } else if (offset_flag == "closetoday" || offset_flag == "CLOSETODAY") {
        offset = THOST_FTDC_OF_CloseToday;
    } else if (offset_flag == "closeyesterday" || offset_flag == "CLOSEYESTERDAY") {
        offset = THOST_FTDC_OF_CloseYesterday;
    }
    
    return m_trade_service->orderInsertComplete(instrument_id.c_str(), price, volume, 
                                               dir, offset, exchange_id.c_str());
}

bool CTPManager::cancelOrder(const std::string& order_ref, const std::string& instrument_id, 
                            const std::string& exchange_id) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    return m_trade_service->orderActionComplete(order_ref.c_str(), instrument_id.c_str(), 
                                               exchange_id.c_str());
}

bool CTPManager::queryAccount(std::vector<CThostFtdcTradingAccountField>& results) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    return m_trade_service->queryTradingAccountComplete(results);
}

bool CTPManager::queryPosition(const std::string& instrument_id, 
                              std::vector<CThostFtdcInvestorPositionField>& results) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    return m_trade_service->queryInvestorPositionComplete(instrument_id.c_str(), results);
}

bool CTPManager::queryInstrument(const std::string& instrument_id, const std::string& exchange_id, 
                                std::vector<CThostFtdcInstrumentField>& results) {
    if (!isTradeReady()) {
        std::cerr << "交易服务未就绪" << std::endl;
        return false;
    }
    
    return m_trade_service->queryInstrumentComplete(instrument_id.c_str(), exchange_id.c_str(), results);
}

bool CTPManager::subscribeMarketData(const std::vector<std::string>& instruments) {
    if (!isMdReady()) {
        std::cerr << "行情服务未就绪" << std::endl;
        return false;
    }
    
    return m_md_service->subscribeMarketDataComplete(instruments);
}

bool CTPManager::unsubscribeMarketData(const std::vector<std::string>& instruments) {
    if (!isMdReady()) {
        std::cerr << "行情服务未就绪" << std::endl;
        return false;
    }
    
    return m_md_service->unsubscribeMarketData(instruments);
}

bool CTPManager::getLatestMarketData(const std::string& instrument_id, 
                                    CThostFtdcDepthMarketDataField& data) {
    if (!isMdReady()) {
        std::cerr << "行情服务未就绪" << std::endl;
        return false;
    }
    
    return m_md_service->getLatestMarketData(instrument_id, data);
}

std::vector<std::string> CTPManager::getSubscribedInstruments() {
    if (!isMdReady()) {
        return {};
    }
    
    return m_md_service->getSubscribedInstruments();
}

// 回调设置
void CTPManager::setOrderCallback(CTPWebService::OrderCallbackDirect callback) {
    m_trade_service->setOrderCallbackDirect(callback);
}

void CTPManager::setTradeCallback(CTPWebService::TradeCallbackDirect callback) {
    m_trade_service->setTradeCallbackDirect(callback);
}

void CTPManager::setMarketDataCallback(CTPMdService::MarketDataCallback callback) {
    m_md_service->setMarketDataCallback(callback);
}

void CTPManager::setSubscribeCallback(CTPMdService::SubscribeCallback callback) {
    m_md_service->setSubscribeCallback(callback);
}

// 内部辅助方法
bool CTPManager::loadConfigFromFile(const std::string& env_config_path) {
    // 简化的YAML配置文件解析
    // 这里实现一个基本的YAML解析，只解析我们需要的字段

    std::ifstream file(env_config_path);
    if (!file.is_open()) {
        std::cerr << "无法打开配置文件: " << env_config_path << std::endl;
        // 使用默认配置
        return loadDefaultConfig();
    }

    std::string line;
    bool in_ctp_section = false;

    // 初始化配置为空
    memset(&m_config, 0, sizeof(m_config));

    while (std::getline(file, line)) {
        // 去除前后空格
        line.erase(0, line.find_first_not_of(" \t"));
        line.erase(line.find_last_not_of(" \t") + 1);

        // 跳过空行和注释
        if (line.empty() || line[0] == '#') {
            continue;
        }

        // 检查是否进入ctp配置段
        if (line == "ctp:") {
            in_ctp_section = true;
            continue;
        }

        // 如果不在ctp段，跳过
        if (!in_ctp_section) {
            continue;
        }

        // 解析配置项
        size_t colon_pos = line.find(':');
        if (colon_pos != std::string::npos) {
            std::string key = line.substr(0, colon_pos);
            std::string value = line.substr(colon_pos + 1);

            // 去除key和value的空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t\""));
            value.erase(value.find_last_not_of(" \t\"") + 1);

            // 设置配置值
            setConfigValue(key, value);
        }
    }

    file.close();

    // 检查必要配置是否存在，如果不存在则使用默认值
    if (strlen(m_config.trade_front) == 0) {
        std::cout << "配置文件中缺少必要配置，使用默认配置" << std::endl;
        return loadDefaultConfig();
    }

    std::cout << "配置加载完成（从文件: " << env_config_path << "）" << std::endl;
    return true;
}

void CTPManager::parseDefaultInstruments() {
    // 解析默认订阅合约字符串
    std::string instruments_str = m_config.default_instruments;
    if (instruments_str.empty()) {
        return;
    }

    std::stringstream ss(instruments_str);
    std::string instrument;
    while (std::getline(ss, instrument, ',')) {
        // 去除前后空格
        instrument.erase(0, instrument.find_first_not_of(" \t"));
        instrument.erase(instrument.find_last_not_of(" \t") + 1);
        if (!instrument.empty()) {
            m_default_instruments.push_back(instrument);
        }
    }

    std::cout << "解析到 " << m_default_instruments.size() << " 个默认订阅合约" << std::endl;
}

void CTPManager::setConfigValue(const std::string& key, const std::string& value) {
    // 设置配置值
    if (key == "trade_front") {
        strncpy(m_config.trade_front, value.c_str(), sizeof(m_config.trade_front) - 1);
    } else if (key == "broker_id") {
        strncpy(m_config.broker_id, value.c_str(), sizeof(m_config.broker_id) - 1);
    } else if (key == "user_id") {
        strncpy(m_config.user_id, value.c_str(), sizeof(m_config.user_id) - 1);
    } else if (key == "password") {
        strncpy(m_config.password, value.c_str(), sizeof(m_config.password) - 1);
    } else if (key == "investor_id") {
        strncpy(m_config.investor_id, value.c_str(), sizeof(m_config.investor_id) - 1);
    } else if (key == "app_id") {
        strncpy(m_config.app_id, value.c_str(), sizeof(m_config.app_id) - 1);
    } else if (key == "auth_code") {
        strncpy(m_config.auth_code, value.c_str(), sizeof(m_config.auth_code) - 1);
    } else if (key == "user_product_info") {
        strncpy(m_config.user_product_info, value.c_str(), sizeof(m_config.user_product_info) - 1);
    } else if (key == "flow_dir") {
        strncpy(m_config.flow_dir, value.c_str(), sizeof(m_config.flow_dir) - 1);
    } else if (key == "md_front") {
        strncpy(m_config.md_front, value.c_str(), sizeof(m_config.md_front) - 1);
    } else if (key == "md_broker_id") {
        strncpy(m_config.md_broker_id, value.c_str(), sizeof(m_config.md_broker_id) - 1);
    } else if (key == "md_user_id") {
        strncpy(m_config.md_user_id, value.c_str(), sizeof(m_config.md_user_id) - 1);
    } else if (key == "md_password") {
        strncpy(m_config.md_password, value.c_str(), sizeof(m_config.md_password) - 1);
    } else if (key == "md_flow_dir") {
        strncpy(m_config.md_flow_dir, value.c_str(), sizeof(m_config.md_flow_dir) - 1);
    } else if (key == "default_instruments") {
        strncpy(m_config.default_instruments, value.c_str(), sizeof(m_config.default_instruments) - 1);
    }
}

bool CTPManager::loadDefaultConfig() {
    // 默认配置
    strcpy(m_config.trade_front, "tcp://10.141.113.102:6661");
    strcpy(m_config.broker_id, "01001");
    strcpy(m_config.user_id, "010000124780");
    strcpy(m_config.password, "000000");
    strcpy(m_config.investor_id, "010000124780");
    strcpy(m_config.app_id, "invest_smart");
    strcpy(m_config.auth_code, "auth123");
    strcpy(m_config.user_product_info, "product_info");
    strcpy(m_config.flow_dir, "./trade_flow");

    // 行情配置
    strcpy(m_config.md_front, "tcp://180.169.30.185:31213");
    strcpy(m_config.md_broker_id, "10010");
    strcpy(m_config.md_user_id, "40090154");
    strcpy(m_config.md_password, "hxqh1234");
    strcpy(m_config.md_flow_dir, "./md_flow");

    // 默认订阅合约
    strcpy(m_config.default_instruments, "IO2508-C-3550,IH2409,CF305");

    std::cout << "使用默认配置" << std::endl;
    return true;
}

#ifndef CTP_MDSERVICE_H
#define CTP_MDSERVICE_H

#include "ThostFtdcMdApi.h"
#include "ThostFtdcUserApiDataType.h"
#include "ThostFtdcUserApiStruct.h"
#include "ctp_config.h"
#include <string>
#include <memory>
#include <vector>
#include <mutex>
#include <condition_variable>
#include <unordered_map>



// CTP 行情服务封装类
class CTPMdService : public CThostFtdcMdSpi {
private:
    // 行情数据存储
    std::unordered_map<std::string, CThostFtdcDepthMarketDataField> m_market_data;
    std::vector<std::string> m_subscribed_instruments;
    
    // 简化响应结构
    struct SimpleResponse {
        bool success;
        std::string error_msg;
    };
    SimpleResponse m_current_response;
    
    void setResponse(bool success, const std::string& error_msg = "");

public:
    // 行情回调函数类型定义
    typedef void (*MarketDataCallback)(const CThostFtdcDepthMarketDataField* market_data);
    typedef void (*SubscribeCallback)(const char* instrument_id, bool success, const char* error_msg);

public:
    CTPMdService();
    virtual ~CTPMdService();

    // 初始化连接
    bool initialize(const CTPConnectionConfig& config);
    
    // CTP行情回调接口实现
    void OnFrontConnected() override;
    void OnFrontDisconnected(int nReason) override;
    void OnRspUserLogin(CThostFtdcRspUserLoginField *pRspUserLogin, 
                       CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspSubMarketData(CThostFtdcSpecificInstrumentField *pSpecificInstrument, 
                           CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspUnSubMarketData(CThostFtdcSpecificInstrumentField *pSpecificInstrument, 
                             CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField *pDepthMarketData) override;
    void OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    
    // 状态查询
    bool isConnected() const { return m_connected; }
    bool isLoggedIn() const { return m_logged_in; }
    
    // 回调设置
    void setMarketDataCallback(MarketDataCallback callback);
    void setSubscribeCallback(SubscribeCallback callback);
    
    // 订阅管理
    bool subscribeMarketData(const std::vector<std::string>& instruments);
    bool unsubscribeMarketData(const std::vector<std::string>& instruments);
    bool subscribeMarketDataComplete(const std::vector<std::string>& instruments);
    
    // 数据访问
    bool getLatestMarketData(const std::string& instrument_id, CThostFtdcDepthMarketDataField& data) const;
    std::vector<std::string> getSubscribedInstruments() const { return m_subscribed_instruments; }
    
    // 等待登录完成
    bool waitForLogin(int timeout_seconds = 30);
    
    // 辅助方法
    bool isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo);
    void waitForResponse(int timeout_seconds = 5);
    void resetResponseState();

private:
    CThostFtdcMdApi* m_api;
    CTPConnectionConfig m_config;
    int m_request_id;
    bool m_connected;
    bool m_logged_in;
    
    // 响应相关成员变量
    bool m_response_ready;
    mutable std::mutex m_mutex;
    std::condition_variable m_cv;
    
    // 回调相关成员
    MarketDataCallback m_market_data_callback;
    SubscribeCallback m_subscribe_callback;
    
    // 内部方法
    void reqUserLogin();
};

#endif // CTP_MDSERVICE_H

#ifndef CTP_CONFIG_H
#define CTP_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

// 确保结构体按1字节对齐
#pragma pack(push, 1)

// 统一的CTP连接配置结构 - 使用C定义
typedef struct {
    char trade_front[256];
    char broker_id[32];
    char user_id[32];
    char password[32];
    char investor_id[32];
    char app_id[32];
    char auth_code[64];
    char user_product_info[64];
    char flow_dir[256];

    // 行情配置
    char md_front[256];
    char md_broker_id[32];
    char md_user_id[32];
    char md_password[32];
    char md_flow_dir[256];

    // 默认订阅合约（以逗号分隔的字符串）
    char default_instruments[1024];
} CTPConnectionConfig;


#pragma pack(pop)

#ifdef __cplusplus
}
#endif

#endif // CTP_CONFIG_H
#include "ctp_direct_interface.h"
#include "ctp_webservice.h"
#include <cstring>
#include <iostream>

extern "C" {
    // 基础服务管理接口
    void* create_ctp_service() {
        return new CTPWebService();
    }

    bool initialize_service(void* service, const CTPConnectionConfig* config) {
        if (!service || !config) {
            return false;
        }

        CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
        return ctp_service->initialize(*config);
    }
    
    void destroy_ctp_service(void* service) {
        CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
        delete ctp_service;
    }
    
    bool is_connected(void* service) {
        CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
        return ctp_service->isConnected();
    }
    
    bool is_logged_in(void* service) {
        CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
        return ctp_service->isLoggedIn();
    }    
}

// 全局存储完整查询结果，避免内存被释放
static std::vector<std::unique_ptr<TradingAccountField[]>> g_complete_account_storage;
static std::vector<std::unique_ptr<InstrumentField[]>> g_complete_instrument_storage;
static std::vector<std::unique_ptr<InvestorPositionField[]>> g_complete_position_storage;
static std::vector<std::unique_ptr<TradeField[]>> g_complete_trade_storage;
static std::vector<std::unique_ptr<OrderField[]>> g_complete_order_storage;
static std::vector<std::unique_ptr<SettlementInfoConfirmField[]>> g_complete_settlement_confirm_storage;
static std::vector<std::unique_ptr<DepthMarketDataField[]>> g_complete_depth_market_data_storage;

// 完整交易接口实现
bool query_trading_account_complete(void* service, TradingAccountField** results, int* count) {
    if (!service || !results || !count) {
        return false;
    }
    
    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
    
    // 使用完整交易接口
    std::vector<CThostFtdcTradingAccountField> accounts;
    bool success = ctp_service->queryTradingAccountComplete(accounts);
    
    *count = accounts.size();
    
    if (!success || accounts.empty()) {
        *results = nullptr;
        return success;
    }
    
    // 创建账户数据数组
    std::unique_ptr<TradingAccountField[]> accounts_data(new TradingAccountField[accounts.size()]);
    
    // 直接复制CTP结构数组
    for (size_t i = 0; i < accounts.size(); i++) {
        accounts_data[i] = accounts[i];
    }
    
    // 存储到全局容器中，避免被释放
    *results = accounts_data.get();
    g_complete_account_storage.push_back(std::move(accounts_data));
    
    return true;
}

bool query_instrument_complete(void* service, const char* instrument_id, const char* exchange_id, 
                              InstrumentField** results, int* count) {
    if (!service || !results || !count) {
        return false;
    }
    
    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
    
    // 使用完整交易接口
    std::vector<CThostFtdcInstrumentField> instruments;
    bool success = ctp_service->queryInstrumentComplete(
        instrument_id ? instrument_id : "", 
        exchange_id ? exchange_id : "", 
        instruments
    );
    
    *count = instruments.size();
    
    if (!success || instruments.empty()) {
        *results = nullptr;
        return success;
    }
    
    // 创建合约数据数组
    std::unique_ptr<InstrumentField[]> instruments_data(new InstrumentField[instruments.size()]);
    
    // 直接复制CTP结构数组并打印完整字节码
    for (size_t i = 0; i < instruments.size(); i++) {
        instruments_data[i] = instruments[i];
  
        // // 打印整个结构体的字节码
        // std::cout << "\n=== 完整结构体字节码 [记录" << i << "] ===" << std::endl;
        // std::cout << "结构体大小: " << sizeof(instruments_data[i]) << " 字节" << std::endl;
        
        // unsigned char* struct_ptr = (unsigned char*)&instruments_data[i];
        // std::cout << "完整字节码: ";
        // for (size_t j = 0; j < sizeof(instruments_data[i]); j++) {
        //     printf("%02X ", struct_ptr[j]);
        //     // 每16字节换行，便于阅读
        //     if ((j + 1) % 16 == 0) {
        //         std::cout << std::endl << "            ";
        //     }
        // }
        // std::cout << std::endl;
    }    
    
    // 存储到全局容器中，避免被释放
    *results = instruments_data.get();
    g_complete_instrument_storage.push_back(std::move(instruments_data));
    
    return true;
}

bool query_investor_position_complete(void* service, const char* instrument_id, 
                                    InvestorPositionField** results, int* count) {
    if (!service || !results || !count) {
        return false;
    }
    
    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
    
    // 使用完整交易接口
    std::vector<CThostFtdcInvestorPositionField> positions;
    bool success = ctp_service->queryInvestorPositionComplete(
        instrument_id ? instrument_id : "", 
        positions
    );
    
    *count = positions.size();
    
    if (!success || positions.empty()) {
        *results = nullptr;
        return success;
    }
    
    // 创建持仓数据数组
    std::unique_ptr<InvestorPositionField[]> positions_data(new InvestorPositionField[positions.size()]);
    
    // 直接复制CTP结构数组
    for (size_t i = 0; i < positions.size(); i++) {
        positions_data[i] = positions[i];
    }
    
    // 存储到全局容器中，避免被释放
    *results = positions_data.get();
    g_complete_position_storage.push_back(std::move(positions_data));
    
    return true;
}

bool query_trade_complete(void* service, const char* instrument_id, TradeField** results, int* count) {
    if (!service || !results || !count) {
        return false;
    }

    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);

    // 使用完整交易接口
    std::vector<CThostFtdcTradeField> trades;
    bool success = ctp_service->queryTradeComplete(
        instrument_id ? instrument_id : "",
        trades
    );

    *count = trades.size();

    if (!success || trades.empty()) {
        *results = nullptr;
        return success;
    }

    // 创建成交数据数组
    std::unique_ptr<TradeField[]> trades_data(new TradeField[trades.size()]);

    // 直接复制CTP结构数组
    for (size_t i = 0; i < trades.size(); i++) {
        trades_data[i] = trades[i];
    }

    // 存储到全局容器中，避免被释放
    *results = trades_data.get();
    g_complete_trade_storage.push_back(std::move(trades_data));

    return true;
}

bool query_order_complete(void* service, const char* instrument_id, OrderField** results, int* count) {
    if (!service || !results || !count) {
        return false;
    }

    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);

    // 使用完整交易接口
    std::vector<CThostFtdcOrderField> orders;
    bool success = ctp_service->queryOrderComplete(
        instrument_id ? instrument_id : "",
        orders
    );

    *count = orders.size();

    if (!success || orders.empty()) {
        *results = nullptr;
        return success;
    }

    // 创建报单数据数组
    std::unique_ptr<OrderField[]> orders_data(new OrderField[orders.size()]);

    // 直接复制CTP结构数组
    for (size_t i = 0; i < orders.size(); i++) {
        orders_data[i] = orders[i];
    }

    // 存储到全局容器中，避免被释放
    *results = orders_data.get();
    g_complete_order_storage.push_back(std::move(orders_data));

    return true;
}

// 释放完整查询结果的内存接口（由于使用了全局存储，这些函数暂时为空）
void free_complete_trading_account_data(TradingAccountField* data) {
    // 数据由全局容器管理，不需要手动释放
}

void free_complete_instrument_data(InstrumentField* data) {
    // 数据由全局容器管理，不需要手动释放
}

void free_complete_position_data(InvestorPositionField* data) {
    // 数据由全局容器管理，不需要手动释放
}

void free_complete_order_data(OrderField* data) {
    // 数据由全局容器管理，不需要手动释放
}

void free_complete_trade_data(TradeField* data) {
    // 数据由全局容器管理，不需要手动释放
}

void free_complete_depth_market_data(DepthMarketDataField* data) {
    // 数据由全局容器管理，不需要手动释放
}

// 完整交易操作接口实现
bool order_insert_complete(void* service, const char* instrument_id, double price, int volume, 
                          char direction, char offset_flag, const char* exchange_id) {
    if (!service) {
        return false;
    }
    
    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
    
    // 使用完整交易接口
    return ctp_service->orderInsertComplete(
        instrument_id, 
        price, 
        volume, 
        direction, 
        offset_flag, 
        exchange_id ? exchange_id : ""
    );
}

bool order_action_complete(void* service, const char* order_ref, const char* instrument_id, 
                          const char* exchange_id) {
    if (!service) {
        return false;
    }
    
    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
    
    // 使用完整交易接口
    return ctp_service->orderActionComplete(
        order_ref ? order_ref : "",
        instrument_id ? instrument_id : "",
        exchange_id ? exchange_id : ""
    );
}

bool query_settlement_info_confirm_complete(void* service, SettlementInfoConfirmField** results, int* count) {
    if (!service || !results || !count) {
        return false;
    }

    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);

    // 清理之前的结果
    ctp_service->clearResults();

    // 发送查询请求
    if (!ctp_service->reqQrySettlementInfoConfirm()) {
        *results = nullptr;
        *count = 0;
        return false;
    }

    // 等待响应
    ctp_service->waitForResponse(5);

    // 获取结果
    const auto& settlement_confirms = ctp_service->getSettlementConfirmResults();
    *count = settlement_confirms.size();

    if (settlement_confirms.empty()) {
        *results = nullptr;
        return true;
    }

    // 分配内存并复制数据
    std::unique_ptr<SettlementInfoConfirmField[]> settlement_confirms_data(new SettlementInfoConfirmField[settlement_confirms.size()]);

    for (size_t i = 0; i < settlement_confirms.size(); ++i) {
        const auto& src = settlement_confirms[i];
        auto& dst = settlement_confirms_data[i];

        // 复制字段
        strncpy(dst.BrokerID, src.BrokerID, sizeof(dst.BrokerID) - 1);
        strncpy(dst.InvestorID, src.InvestorID, sizeof(dst.InvestorID) - 1);
        strncpy(dst.ConfirmDate, src.ConfirmDate, sizeof(dst.ConfirmDate) - 1);
        strncpy(dst.ConfirmTime, src.ConfirmTime, sizeof(dst.ConfirmTime) - 1);
        dst.SettlementID = src.SettlementID;
        strncpy(dst.AccountID, src.AccountID, sizeof(dst.AccountID) - 1);
        strncpy(dst.CurrencyID, src.CurrencyID, sizeof(dst.CurrencyID) - 1);

        // 确保字符串以null结尾
        dst.BrokerID[sizeof(dst.BrokerID) - 1] = '\0';
        dst.InvestorID[sizeof(dst.InvestorID) - 1] = '\0';
        dst.ConfirmDate[sizeof(dst.ConfirmDate) - 1] = '\0';
        dst.ConfirmTime[sizeof(dst.ConfirmTime) - 1] = '\0';
        dst.AccountID[sizeof(dst.AccountID) - 1] = '\0';
        dst.CurrencyID[sizeof(dst.CurrencyID) - 1] = '\0';
    }

    *results = settlement_confirms_data.get();

    // 保存到全局存储，避免内存被释放
    g_complete_settlement_confirm_storage.push_back(std::move(settlement_confirms_data));

    return true;
}

bool query_depth_market_data_complete(void* service, const char* instrument_id, const char* exchange_id,
                                     DepthMarketDataField** results, int* count) {
    if (!service || !results || !count) {
        return false;
    }

    CTPWebService* ctp_service = static_cast<CTPWebService*>(service);
    std::vector<CThostFtdcDepthMarketDataField> depth_market_data;

    // 调用查询方法
    if (!ctp_service->queryDepthMarketDataComplete(instrument_id, exchange_id, depth_market_data)) {
        *results = nullptr;
        *count = 0;
        return false;
    }

    *count = depth_market_data.size();
    if (*count == 0) {
        *results = nullptr;
        return true;
    }

    // 分配内存并复制数据
    std::unique_ptr<DepthMarketDataField[]> depth_market_data_data(new DepthMarketDataField[depth_market_data.size()]);

    for (size_t i = 0; i < depth_market_data.size(); ++i) {
        depth_market_data_data[i] = depth_market_data[i];
    }

    *results = depth_market_data_data.get();

    // 保存到全局存储，避免内存被释放
    g_complete_depth_market_data_storage.push_back(std::move(depth_market_data_data));

    return true;
}


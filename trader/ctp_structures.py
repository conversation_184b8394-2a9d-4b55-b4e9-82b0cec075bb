#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTP结构体定义带padding完整版本
包含字段名、类型、大小、对齐和中文名称
"""
import ctypes

# === TradingAccountField ===
class TradingAccountField(ctypes.Structure):
    """资金账户数据结构 - 对应CThostFtdcTradingAccountField"""
    _pack_ = 1
    _fields_ = [
        ("BrokerID", ctypes.c_char * 11),  # TThostFtdcBrokerIDType - 经纪公司代码
        ("AccountID", ctypes.c_char * 13),  # TThostFtdcAccountIDType - 投资者帐号
        ("PreMortgage", ctypes.c_double),  # TThostFtdcMoneyType - 上次质押金额
        ("PreCredit", ctypes.c_double),  # TThostFtdcMoneyType - 上次信用额度
        ("PreDeposit", ctypes.c_double),  # TThostFtdcMoneyType - 上次存款额
        ("PreBalance", ctypes.c_double),  # TThostFtdcMoneyType - 上次结算准备金
        ("PreMargin", ctypes.c_double),  # TThostFtdcMoneyType - 上次占用的保证金
        ("InterestBase", ctypes.c_double),  # TThostFtdcMoneyType - 利息基数
        ("Interest", ctypes.c_double),  # TThostFtdcMoneyType - 利息收入
        ("Deposit", ctypes.c_double),  # TThostFtdcMoneyType - 入金金额
        ("Withdraw", ctypes.c_double),  # TThostFtdcMoneyType - 出金金额
        ("FrozenMargin", ctypes.c_double),  # TThostFtdcMoneyType - 冻结的保证金
        ("FrozenCash", ctypes.c_double),  # TThostFtdcMoneyType - 冻结的资金
        ("FrozenCommission", ctypes.c_double),  # TThostFtdcMoneyType - 冻结的手续费
        ("CurrMargin", ctypes.c_double),  # TThostFtdcMoneyType - 当前保证金总额
        ("CashIn", ctypes.c_double),  # TThostFtdcMoneyType - 资金差额
        ("Commission", ctypes.c_double),  # TThostFtdcMoneyType - 手续费
        ("CloseProfit", ctypes.c_double),  # TThostFtdcMoneyType - 平仓盈亏
        ("PositionProfit", ctypes.c_double),  # TThostFtdcMoneyType - 持仓盈亏
        ("Balance", ctypes.c_double),  # TThostFtdcMoneyType - 期货结算准备金
        ("Available", ctypes.c_double),  # TThostFtdcMoneyType - 可用资金
        ("WithdrawQuota", ctypes.c_double),  # TThostFtdcMoneyType - 可取资金
        ("Reserve", ctypes.c_double),  # TThostFtdcMoneyType - 基本准备金
        ("TradingDay", ctypes.c_char * 9),  # TThostFtdcDateType - 交易日
        ("_padding_0", ctypes.c_char * 3),  # 对齐填充3字节
        ("SettlementID", ctypes.c_int),  # TThostFtdcSettlementIDType - 结算编号
        ("Credit", ctypes.c_double),  # TThostFtdcMoneyType - 信用额度
        ("Mortgage", ctypes.c_double),  # TThostFtdcMoneyType - 质押金额
        ("ExchangeMargin", ctypes.c_double),  # TThostFtdcMoneyType - 交易所保证金
        ("DeliveryMargin", ctypes.c_double),  # TThostFtdcMoneyType - 投资者交割保证金
        ("ExchangeDeliveryMargin", ctypes.c_double),  # TThostFtdcMoneyType - 交易所交割保证金
        ("ReserveBalance", ctypes.c_double),  # TThostFtdcMoneyType - 保底期货结算准备金
        ("CurrencyID", ctypes.c_char * 4),  # TThostFtdcCurrencyIDType - 币种代码
        ("_padding_1", ctypes.c_char * 4),  # 对齐填充4字节
        ("PreFundMortgageIn", ctypes.c_double),  # TThostFtdcMoneyType - 上次货币质入金额
        ("PreFundMortgageOut", ctypes.c_double),  # TThostFtdcMoneyType - 上次货币质出金额
        ("FundMortgageIn", ctypes.c_double),  # TThostFtdcMoneyType - 货币质入金额
        ("FundMortgageOut", ctypes.c_double),  # TThostFtdcMoneyType - 货币质出金额
        ("FundMortgageAvailable", ctypes.c_double),  # TThostFtdcMoneyType - 货币质押余额
        ("MortgageableFund", ctypes.c_double),  # TThostFtdcMoneyType - 可质押货币金额
        ("SpecProductMargin", ctypes.c_double),  # TThostFtdcMoneyType - 特殊产品占用保证金
        ("SpecProductFrozenMargin", ctypes.c_double),  # TThostFtdcMoneyType - 特殊产品冻结保证金
        ("SpecProductCommission", ctypes.c_double),  # TThostFtdcMoneyType - 特殊产品手续费
        ("SpecProductFrozenCommission", ctypes.c_double),  # TThostFtdcMoneyType - 特殊产品冻结手续费
        ("SpecProductPositionProfit", ctypes.c_double),  # TThostFtdcMoneyType - 特殊产品持仓盈亏
        ("SpecProductCloseProfit", ctypes.c_double),  # TThostFtdcMoneyType - 特殊产品平仓盈亏
        ("SpecProductPositionProfitByAlg", ctypes.c_double),  # TThostFtdcMoneyType - 根据持仓盈亏算法计算的特殊产品持仓盈亏
        ("SpecProductExchangeMargin", ctypes.c_double),  # TThostFtdcMoneyType - 特殊产品交易所保证金
        ("BizType", ctypes.c_char),  # TThostFtdcBizTypeType - 业务类型
        ("_padding_2", ctypes.c_char * 7),  # 对齐填充7字节
        ("FrozenSwap", ctypes.c_double),  # TThostFtdcMoneyType - 延时换汇冻结金额
        ("RemainSwap", ctypes.c_double),  # TThostFtdcMoneyType - 剩余换汇额度
    ]
    # 原始大小: 400 字节，对齐后大小: 400 字节

# TradingAccountField 字段中文名映射
TRADINGACCOUNTFIELD_FIELD_NAMES_CN = {
    "BrokerID": "经纪公司代码",
    "AccountID": "投资者帐号",
    "PreMortgage": "上次质押金额",
    "PreCredit": "上次信用额度",
    "PreDeposit": "上次存款额",
    "PreBalance": "上次结算准备金",
    "PreMargin": "上次占用的保证金",
    "InterestBase": "利息基数",
    "Interest": "利息收入",
    "Deposit": "入金金额",
    "Withdraw": "出金金额",
    "FrozenMargin": "冻结的保证金",
    "FrozenCash": "冻结的资金",
    "FrozenCommission": "冻结的手续费",
    "CurrMargin": "当前保证金总额",
    "CashIn": "资金差额",
    "Commission": "手续费",
    "CloseProfit": "平仓盈亏",
    "PositionProfit": "持仓盈亏",
    "Balance": "期货结算准备金",
    "Available": "可用资金",
    "WithdrawQuota": "可取资金",
    "Reserve": "基本准备金",
    "TradingDay": "交易日",
    "SettlementID": "结算编号",
    "Credit": "信用额度",
    "Mortgage": "质押金额",
    "ExchangeMargin": "交易所保证金",
    "DeliveryMargin": "投资者交割保证金",
    "ExchangeDeliveryMargin": "交易所交割保证金",
    "ReserveBalance": "保底期货结算准备金",
    "CurrencyID": "币种代码",
    "PreFundMortgageIn": "上次货币质入金额",
    "PreFundMortgageOut": "上次货币质出金额",
    "FundMortgageIn": "货币质入金额",
    "FundMortgageOut": "货币质出金额",
    "FundMortgageAvailable": "货币质押余额",
    "MortgageableFund": "可质押货币金额",
    "SpecProductMargin": "特殊产品占用保证金",
    "SpecProductFrozenMargin": "特殊产品冻结保证金",
    "SpecProductCommission": "特殊产品手续费",
    "SpecProductFrozenCommission": "特殊产品冻结手续费",
    "SpecProductPositionProfit": "特殊产品持仓盈亏",
    "SpecProductCloseProfit": "特殊产品平仓盈亏",
    "SpecProductPositionProfitByAlg": "根据持仓盈亏算法计算的特殊产品持仓盈亏",
    "SpecProductExchangeMargin": "特殊产品交易所保证金",
    "BizType": "业务类型",
    "FrozenSwap": "延时换汇冻结金额",
    "RemainSwap": "剩余换汇额度",
}

# === DepthMarketDataField ===
class DepthMarketDataField(ctypes.Structure):
    """深度行情数据结构 - 对应CThostFtdcDepthMarketDataField"""
    _pack_ = 1
    _fields_ = [
        ("TradingDay", ctypes.c_char * 9),  # TThostFtdcDateType - 交易日
        ("reserve1", ctypes.c_char * 31),  # TThostFtdcOldInstrumentIDType - 保留的无效字段
        ("ExchangeID", ctypes.c_char * 9),  # TThostFtdcExchangeIDType - 交易所代码
        ("reserve2", ctypes.c_char * 31),  # TThostFtdcOldExchangeInstIDType - 保留的无效字段
        ("LastPrice", ctypes.c_double),  # TThostFtdcPriceType - 最新价
        ("PreSettlementPrice", ctypes.c_double),  # TThostFtdcPriceType - 上次结算价
        ("PreClosePrice", ctypes.c_double),  # TThostFtdcPriceType - 昨收盘
        ("PreOpenInterest", ctypes.c_double),  # TThostFtdcLargeVolumeType - 昨持仓量
        ("OpenPrice", ctypes.c_double),  # TThostFtdcPriceType - 今开盘
        ("HighestPrice", ctypes.c_double),  # TThostFtdcPriceType - 最高价
        ("LowestPrice", ctypes.c_double),  # TThostFtdcPriceType - 最低价
        ("Volume", ctypes.c_int),  # TThostFtdcVolumeType - 数量
        ("Turnover", ctypes.c_double),  # TThostFtdcMoneyType - 成交金额
        ("OpenInterest", ctypes.c_double),  # TThostFtdcLargeVolumeType - 持仓量
        ("ClosePrice", ctypes.c_double),  # TThostFtdcPriceType - 今收盘
        ("SettlementPrice", ctypes.c_double),  # TThostFtdcPriceType - 本次结算价
        ("UpperLimitPrice", ctypes.c_double),  # TThostFtdcPriceType - 涨停板价
        ("LowerLimitPrice", ctypes.c_double),  # TThostFtdcPriceType - 跌停板价
        ("PreDelta", ctypes.c_double),  # TThostFtdcRatioType - 昨虚实度
        ("CurrDelta", ctypes.c_double),  # TThostFtdcRatioType - 今虚实度
        ("UpdateTime", ctypes.c_char * 9),  # TThostFtdcTimeType - 最后修改时间
        ("UpdateMillisec", ctypes.c_int),  # TThostFtdcMillisecType - 最后修改毫秒
        ("BidPrice1", ctypes.c_double),  # TThostFtdcPriceType - 申买价一
        ("BidVolume1", ctypes.c_int),  # TThostFtdcVolumeType - 申买量一
        ("AskPrice1", ctypes.c_double),  # TThostFtdcPriceType - 申卖价一
        ("AskVolume1", ctypes.c_int),  # TThostFtdcVolumeType - 申卖量一
        ("BidPrice2", ctypes.c_double),  # TThostFtdcPriceType - 申买价二
        ("BidVolume2", ctypes.c_int),  # TThostFtdcVolumeType - 申买量二
        ("AskPrice2", ctypes.c_double),  # TThostFtdcPriceType - 申卖价二
        ("AskVolume2", ctypes.c_int),  # TThostFtdcVolumeType - 申卖量二
        ("BidPrice3", ctypes.c_double),  # TThostFtdcPriceType - 申买价三
        ("BidVolume3", ctypes.c_int),  # TThostFtdcVolumeType - 申买量三
        ("AskPrice3", ctypes.c_double),  # TThostFtdcPriceType - 申卖价三
        ("AskVolume3", ctypes.c_int),  # TThostFtdcVolumeType - 申卖量三
        ("BidPrice4", ctypes.c_double),  # TThostFtdcPriceType - 申买价四
        ("BidVolume4", ctypes.c_int),  # TThostFtdcVolumeType - 申买量四
        ("AskPrice4", ctypes.c_double),  # TThostFtdcPriceType - 申卖价四
        ("AskVolume4", ctypes.c_int),  # TThostFtdcVolumeType - 申卖量四
        ("BidPrice5", ctypes.c_double),  # TThostFtdcPriceType - 申买价五
        ("BidVolume5", ctypes.c_int),  # TThostFtdcVolumeType - 申买量五
        ("AskPrice5", ctypes.c_double),  # TThostFtdcPriceType - 申卖价五
        ("AskVolume5", ctypes.c_int),  # TThostFtdcVolumeType - 申卖量五
        ("AveragePrice", ctypes.c_double),  # TThostFtdcPriceType - 当日均价
        ("ActionDay", ctypes.c_char * 9),  # TThostFtdcDateType - 业务日期
        ("InstrumentID", ctypes.c_char * 81),  # TThostFtdcInstrumentIDType - 合约代码
        ("ExchangeInstID", ctypes.c_char * 81),  # TThostFtdcExchangeInstIDType - 合约在交易所的代码
        ("BandingUpperPrice", ctypes.c_double),  # TThostFtdcPriceType - 上带价
        ("BandingLowerPrice", ctypes.c_double),  # TThostFtdcPriceType - 下带价
    ]

# DepthMarketDataField 字段中文名映射
DEPTHMARKETDATAFIELD_FIELD_NAMES_CN = {
    "TradingDay": "交易日",
    "reserve1": "保留的无效字段",
    "ExchangeID": "交易所代码",
    "reserve2": "保留的无效字段",
    "LastPrice": "最新价",
    "PreSettlementPrice": "上次结算价",
    "PreClosePrice": "昨收盘",
    "PreOpenInterest": "昨持仓量",
    "OpenPrice": "今开盘",
    "HighestPrice": "最高价",
    "LowestPrice": "最低价",
    "Volume": "数量",
    "Turnover": "成交金额",
    "OpenInterest": "持仓量",
    "ClosePrice": "今收盘",
    "SettlementPrice": "本次结算价",
    "UpperLimitPrice": "涨停板价",
    "LowerLimitPrice": "跌停板价",
    "PreDelta": "昨虚实度",
    "CurrDelta": "今虚实度",
    "UpdateTime": "最后修改时间",
    "UpdateMillisec": "最后修改毫秒",
    "BidPrice1": "申买价一",
    "BidVolume1": "申买量一",
    "AskPrice1": "申卖价一",
    "AskVolume1": "申卖量一",
    "BidPrice2": "申买价二",
    "BidVolume2": "申买量二",
    "AskPrice2": "申卖价二",
    "AskVolume2": "申卖量二",
    "BidPrice3": "申买价三",
    "BidVolume3": "申买量三",
    "AskPrice3": "申卖价三",
    "AskVolume3": "申卖量三",
    "BidPrice4": "申买价四",
    "BidVolume4": "申买量四",
    "AskPrice4": "申卖价四",
    "AskVolume4": "申卖量四",
    "BidPrice5": "申买价五",
    "BidVolume5": "申买量五",
    "AskPrice5": "申卖价五",
    "AskVolume5": "申卖量五",
    "AveragePrice": "当日均价",
    "ActionDay": "业务日期",
    "InstrumentID": "合约代码",
    "ExchangeInstID": "合约在交易所的代码",
    "BandingUpperPrice": "上带价",
    "BandingLowerPrice": "下带价",
}

# === QryDepthMarketDataField ===
class QryDepthMarketDataField(ctypes.Structure):
    """查询深度行情数据结构 - 对应CThostFtdcQryDepthMarketDataField"""
    _pack_ = 1
    _fields_ = [
        ("reserve1", ctypes.c_char * 31),  # TThostFtdcOldInstrumentIDType - 保留的无效字段
        ("ExchangeID", ctypes.c_char * 9),  # TThostFtdcExchangeIDType - 交易所代码
        ("InstrumentID", ctypes.c_char * 81),  # TThostFtdcInstrumentIDType - 合约代码
    ]

# QryDepthMarketDataField 字段中文名映射
QRYDEPTHMARKETDATAFIELD_FIELD_NAMES_CN = {
    "reserve1": "保留的无效字段",
    "ExchangeID": "交易所代码",
    "InstrumentID": "合约代码",
}

# === InstrumentField ===
class InstrumentField(ctypes.Structure):
    """合约数据结构 - 对应CThostFtdcInstrumentField"""
    _pack_ = 1
    _fields_ = [
        ("reserve1", ctypes.c_char * 31),  # TThostFtdcOldInstrumentIDType - 保留的无效字段
        ("ExchangeID", ctypes.c_char * 9),  # TThostFtdcExchangeIDType - 交易所代码
        ("InstrumentName", ctypes.c_char * 21),  # TThostFtdcInstrumentNameType - 合约名称
        ("reserve2", ctypes.c_char * 31),  # TThostFtdcOldExchangeInstIDType - 保留的无效字段
        ("reserve3", ctypes.c_char * 31),  # TThostFtdcOldInstrumentIDType - 保留的无效字段
        ("ProductClass", ctypes.c_char),  # TThostFtdcProductClassType - 产品类型
        ("DeliveryYear", ctypes.c_int),  # TThostFtdcYearType - 交割年份
        ("DeliveryMonth", ctypes.c_int),  # TThostFtdcMonthType - 交割月
        ("MaxMarketOrderVolume", ctypes.c_int),  # TThostFtdcVolumeType - 市价单最大下单量
        ("MinMarketOrderVolume", ctypes.c_int),  # TThostFtdcVolumeType - 市价单最小下单量
        ("MaxLimitOrderVolume", ctypes.c_int),  # TThostFtdcVolumeType - 限价单最大下单量
        ("MinLimitOrderVolume", ctypes.c_int),  # TThostFtdcVolumeType - 限价单最小下单量
        ("VolumeMultiple", ctypes.c_int),  # TThostFtdcVolumeMultipleType - 合约数量乘数
        ("PriceTick", ctypes.c_double),  # TThostFtdcPriceType - 最小变动价位
        ("CreateDate", ctypes.c_char * 9),  # TThostFtdcDateType - 创建日
        ("OpenDate", ctypes.c_char * 9),  # TThostFtdcDateType - 上市日
        ("ExpireDate", ctypes.c_char * 9),  # TThostFtdcDateType - 到期日
        ("StartDelivDate", ctypes.c_char * 9),  # TThostFtdcDateType - 开始交割日
        ("EndDelivDate", ctypes.c_char * 9),  # TThostFtdcDateType - 结束交割日
        ("InstLifePhase", ctypes.c_char),  # TThostFtdcInstLifePhaseType - 合约生命周期状态
        ("_padding_0", ctypes.c_char * 2),  # 对齐填充2字节
        ("IsTrading", ctypes.c_int),  # TThostFtdcBoolType - 当前是否交易
        ("PositionType", ctypes.c_char),  # TThostFtdcPositionTypeType - 持仓类型
        ("PositionDateType", ctypes.c_char),  # TThostFtdcPositionDateTypeType - 持仓日期类型
        ("_padding_1", ctypes.c_char * 2),  # 对齐填充2字节
        ("LongMarginRatio", ctypes.c_double),  # TThostFtdcRatioType - 多头保证金率
        ("ShortMarginRatio", ctypes.c_double),  # TThostFtdcRatioType - 空头保证金率
        ("MaxMarginSideAlgorithm", ctypes.c_char),  # TThostFtdcMaxMarginSideAlgorithmType - 是否使用大额单边保证金算法
        ("reserve4", ctypes.c_char * 31),  # TThostFtdcOldInstrumentIDType - 保留的无效字段
        ("StrikePrice", ctypes.c_double),  # TThostFtdcPriceType - 执行价
        ("OptionsType", ctypes.c_char),  # TThostFtdcOptionsTypeType - 期权类型
        ("_padding_2", ctypes.c_char * 7),  # 对齐填充7字节
        ("UnderlyingMultiple", ctypes.c_double),  # TThostFtdcUnderlyingMultipleType - 合约基础商品乘数
        ("CombinationType", ctypes.c_char),  # TThostFtdcCombinationTypeType - 组合类型
        ("InstrumentID", ctypes.c_char * 81),  # TThostFtdcInstrumentIDType - 合约代码
        ("ExchangeInstID", ctypes.c_char * 81),  # TThostFtdcExchangeInstIDType - 合约在交易所的代码
        ("ProductID", ctypes.c_char * 81),  # TThostFtdcInstrumentIDType - 产品代码
        ("UnderlyingInstrID", ctypes.c_char * 81),  # TThostFtdcInstrumentIDType - 基础商品代码
        ("_padding_final", ctypes.c_char * 3),  # 结构体末尾填充3字节
    ]
    # 原始大小: 613 字节，对齐后大小: 616 字节

# InstrumentField 字段中文名映射
INSTRUMENTFIELD_FIELD_NAMES_CN = {
    "reserve1": "保留的无效字段",
    "ExchangeID": "交易所代码",
    "InstrumentName": "合约名称",
    "reserve2": "保留的无效字段",
    "reserve3": "保留的无效字段",
    "ProductClass": "产品类型",
    "DeliveryYear": "交割年份",
    "DeliveryMonth": "交割月",
    "MaxMarketOrderVolume": "市价单最大下单量",
    "MinMarketOrderVolume": "市价单最小下单量",
    "MaxLimitOrderVolume": "限价单最大下单量",
    "MinLimitOrderVolume": "限价单最小下单量",
    "VolumeMultiple": "合约数量乘数",
    "PriceTick": "最小变动价位",
    "CreateDate": "创建日",
    "OpenDate": "上市日",
    "ExpireDate": "到期日",
    "StartDelivDate": "开始交割日",
    "EndDelivDate": "结束交割日",
    "InstLifePhase": "合约生命周期状态",
    "IsTrading": "当前是否交易",
    "PositionType": "持仓类型",
    "PositionDateType": "持仓日期类型",
    "LongMarginRatio": "多头保证金率",
    "ShortMarginRatio": "空头保证金率",
    "MaxMarginSideAlgorithm": "是否使用大额单边保证金算法",
    "reserve4": "保留的无效字段",
    "StrikePrice": "执行价",
    "OptionsType": "期权类型",
    "UnderlyingMultiple": "合约基础商品乘数",
    "CombinationType": "组合类型",
    "InstrumentID": "合约代码",
    "ExchangeInstID": "合约在交易所的代码",
    "ProductID": "产品代码",
    "UnderlyingInstrID": "基础商品代码",
}

# === InvestorPositionField ===
class InvestorPositionField(ctypes.Structure):
    """持仓数据结构 - 对应CThostFtdcInvestorPositionField"""
    _pack_ = 1
    _fields_ = [
        ("reserve1", ctypes.c_char * 31),  # TThostFtdcOldInstrumentIDType - 保留的无效字段
        ("BrokerID", ctypes.c_char * 11),  # TThostFtdcBrokerIDType - 经纪公司代码
        ("InvestorID", ctypes.c_char * 13),  # TThostFtdcInvestorIDType - 投资者代码
        ("PosiDirection", ctypes.c_char),  # TThostFtdcPosiDirectionType - 持仓多空方向
        ("HedgeFlag", ctypes.c_char),  # TThostFtdcHedgeFlagType - 投机套保标志
        ("PositionDate", ctypes.c_char),  # TThostFtdcPositionDateType - 持仓日期
        ("_padding_0", ctypes.c_char * 2),  # 对齐填充2字节
        ("YdPosition", ctypes.c_int),  # TThostFtdcVolumeType - 上日持仓
        ("Position", ctypes.c_int),  # TThostFtdcVolumeType - 今日持仓
        ("LongFrozen", ctypes.c_int),  # TThostFtdcVolumeType - 多头冻结
        ("ShortFrozen", ctypes.c_int),  # TThostFtdcVolumeType - 空头冻结
        ("_padding_1", ctypes.c_char * 4),  # 对齐填充4字节
        ("LongFrozenAmount", ctypes.c_double),  # TThostFtdcMoneyType - 开仓冻结金额
        ("ShortFrozenAmount", ctypes.c_double),  # TThostFtdcMoneyType - 开仓冻结金额
        ("OpenVolume", ctypes.c_int),  # TThostFtdcVolumeType - 开仓量
        ("CloseVolume", ctypes.c_int),  # TThostFtdcVolumeType - 平仓量
        ("OpenAmount", ctypes.c_double),  # TThostFtdcMoneyType - 开仓金额
        ("CloseAmount", ctypes.c_double),  # TThostFtdcMoneyType - 平仓金额
        ("PositionCost", ctypes.c_double),  # TThostFtdcMoneyType - 持仓成本
        ("PreMargin", ctypes.c_double),  # TThostFtdcMoneyType - 上次占用的保证金
        ("UseMargin", ctypes.c_double),  # TThostFtdcMoneyType - 占用的保证金
        ("FrozenMargin", ctypes.c_double),  # TThostFtdcMoneyType - 冻结的保证金
        ("FrozenCash", ctypes.c_double),  # TThostFtdcMoneyType - 冻结的资金
        ("FrozenCommission", ctypes.c_double),  # TThostFtdcMoneyType - 冻结的手续费
        ("CashIn", ctypes.c_double),  # TThostFtdcMoneyType - 资金差额
        ("Commission", ctypes.c_double),  # TThostFtdcMoneyType - 手续费
        ("CloseProfit", ctypes.c_double),  # TThostFtdcMoneyType - 平仓盈亏
        ("PositionProfit", ctypes.c_double),  # TThostFtdcMoneyType - 持仓盈亏
        ("PreSettlementPrice", ctypes.c_double),  # TThostFtdcPriceType - 上次结算价
        ("SettlementPrice", ctypes.c_double),  # TThostFtdcPriceType - 本次结算价
        ("TradingDay", ctypes.c_char * 9),  # TThostFtdcDateType - 交易日
        ("_padding_2", ctypes.c_char * 3),  # 对齐填充3字节
        ("SettlementID", ctypes.c_int),  # TThostFtdcSettlementIDType - 结算编号
        ("OpenCost", ctypes.c_double),  # TThostFtdcMoneyType - 开仓成本
        ("ExchangeMargin", ctypes.c_double),  # TThostFtdcMoneyType - 交易所保证金
        ("CombPosition", ctypes.c_int),  # TThostFtdcVolumeType - 组合成交形成的持仓
        ("CombLongFrozen", ctypes.c_int),  # TThostFtdcVolumeType - 组合多头冻结
        ("CombShortFrozen", ctypes.c_int),  # TThostFtdcVolumeType - 组合空头冻结
        ("_padding_3", ctypes.c_char * 4),  # 对齐填充4字节
        ("CloseProfitByDate", ctypes.c_double),  # TThostFtdcMoneyType - 逐日盯市平仓盈亏
        ("CloseProfitByTrade", ctypes.c_double),  # TThostFtdcMoneyType - 逐笔对冲平仓盈亏
        ("TodayPosition", ctypes.c_int),  # TThostFtdcVolumeType - 今日持仓
        ("_padding_4", ctypes.c_char * 4),  # 对齐填充4字节
        ("MarginRateByMoney", ctypes.c_double),  # TThostFtdcRatioType - 保证金率
        ("MarginRateByVolume", ctypes.c_double),  # TThostFtdcRatioType - 保证金率按手数
        ("StrikeFrozen", ctypes.c_int),  # TThostFtdcVolumeType - 执行冻结
        ("_padding_5", ctypes.c_char * 4),  # 对齐填充4字节
        ("StrikeFrozenAmount", ctypes.c_double),  # TThostFtdcMoneyType - 执行冻结金额
        ("AbandonFrozen", ctypes.c_int),  # TThostFtdcVolumeType - 放弃执行冻结
        ("ExchangeID", ctypes.c_char * 9),  # TThostFtdcExchangeIDType - 交易所代码
        ("_padding_6", ctypes.c_char * 3),  # 对齐填充3字节
        ("YdStrikeFrozen", ctypes.c_int),  # TThostFtdcVolumeType - 执行冻结的昨仓
        ("InvestUnitID", ctypes.c_char * 17),  # TThostFtdcInvestUnitIDType - 投资单元代码
        ("_padding_7", ctypes.c_char * 3),  # 对齐填充3字节
        ("PositionCostOffset", ctypes.c_double),  # TThostFtdcMoneyType - 大商所持仓成本差值只有大商所使用
        ("TasPosition", ctypes.c_int),  # TThostFtdcVolumeType - 持仓手数
        ("_padding_8", ctypes.c_char * 4),  # 对齐填充4字节
        ("TasPositionCost", ctypes.c_double),  # TThostFtdcMoneyType - 持仓成本
        ("InstrumentID", ctypes.c_char * 81),  # TThostFtdcInstrumentIDType - 合约代码
        ("_padding_final", ctypes.c_char * 7),  # 结构体末尾填充7字节
    ]
    # 原始大小: 465 字节，对齐后大小: 472 字节

# InvestorPositionField 字段中文名映射
INVESTORPOSITIONFIELD_FIELD_NAMES_CN = {
    "reserve1": "保留的无效字段",
    "BrokerID": "经纪公司代码",
    "InvestorID": "投资者代码",
    "PosiDirection": "持仓多空方向",
    "HedgeFlag": "投机套保标志",
    "PositionDate": "持仓日期",
    "YdPosition": "上日持仓",
    "Position": "今日持仓",
    "LongFrozen": "多头冻结",
    "ShortFrozen": "空头冻结",
    "LongFrozenAmount": "开仓冻结金额",
    "ShortFrozenAmount": "开仓冻结金额",
    "OpenVolume": "开仓量",
    "CloseVolume": "平仓量",
    "OpenAmount": "开仓金额",
    "CloseAmount": "平仓金额",
    "PositionCost": "持仓成本",
    "PreMargin": "上次占用的保证金",
    "UseMargin": "占用的保证金",
    "FrozenMargin": "冻结的保证金",
    "FrozenCash": "冻结的资金",
    "FrozenCommission": "冻结的手续费",
    "CashIn": "资金差额",
    "Commission": "手续费",
    "CloseProfit": "平仓盈亏",
    "PositionProfit": "持仓盈亏",
    "PreSettlementPrice": "上次结算价",
    "SettlementPrice": "本次结算价",
    "TradingDay": "交易日",
    "SettlementID": "结算编号",
    "OpenCost": "开仓成本",
    "ExchangeMargin": "交易所保证金",
    "CombPosition": "组合成交形成的持仓",
    "CombLongFrozen": "组合多头冻结",
    "CombShortFrozen": "组合空头冻结",
    "CloseProfitByDate": "逐日盯市平仓盈亏",
    "CloseProfitByTrade": "逐笔对冲平仓盈亏",
    "TodayPosition": "今日持仓",
    "MarginRateByMoney": "保证金率",
    "MarginRateByVolume": "保证金率按手数",
    "StrikeFrozen": "执行冻结",
    "StrikeFrozenAmount": "执行冻结金额",
    "AbandonFrozen": "放弃执行冻结",
    "ExchangeID": "交易所代码",
    "YdStrikeFrozen": "执行冻结的昨仓",
    "InvestUnitID": "投资单元代码",
    "PositionCostOffset": "大商所持仓成本差值只有大商所使用",
    "TasPosition": "持仓手数",
    "TasPositionCost": "持仓成本",
    "InstrumentID": "合约代码",
}

# === TradeField ===
class TradeField(ctypes.Structure):
    """成交数据结构 - 对应CThostFtdcTradeField"""
    _pack_ = 1
    _fields_ = [
        ("BrokerID", ctypes.c_char * 11),  # TThostFtdcBrokerIDType - 经纪公司代码
        ("InvestorID", ctypes.c_char * 13),  # TThostFtdcInvestorIDType - 投资者代码
        ("reserve1", ctypes.c_char * 31),  # TThostFtdcOldInstrumentIDType - 保留的无效字段
        ("OrderRef", ctypes.c_char * 13),  # TThostFtdcOrderRefType - 报单引用
        ("UserID", ctypes.c_char * 16),  # TThostFtdcUserIDType - 用户代码
        ("ExchangeID", ctypes.c_char * 9),  # TThostFtdcExchangeIDType - 交易所代码
        ("TradeID", ctypes.c_char * 21),  # TThostFtdcTradeIDType - 成交编号
        ("Direction", ctypes.c_char),  # TThostFtdcDirectionType - 买卖方向
        ("OrderSysID", ctypes.c_char * 21),  # TThostFtdcOrderSysIDType - 报单编号
        ("ParticipantID", ctypes.c_char * 11),  # TThostFtdcParticipantIDType - 会员代码
        ("ClientID", ctypes.c_char * 11),  # TThostFtdcClientIDType - 客户代码
        ("TradingRole", ctypes.c_char),  # TThostFtdcTradingRoleType - 交易角色
        ("reserve2", ctypes.c_char * 31),  # TThostFtdcOldExchangeInstIDType - 保留的无效字段
        ("OffsetFlag", ctypes.c_char),  # TThostFtdcOffsetFlagType - 开平标志
        ("HedgeFlag", ctypes.c_char),  # TThostFtdcHedgeFlagType - 投机套保标志
        ("Price", ctypes.c_double),  # TThostFtdcPriceType - 价格
        ("Volume", ctypes.c_int),  # TThostFtdcVolumeType - 数量
        ("TradeDate", ctypes.c_char * 9),  # TThostFtdcDateType - 成交时期
        ("TradeTime", ctypes.c_char * 9),  # TThostFtdcTimeType - 成交时间
        ("TradeType", ctypes.c_char),  # TThostFtdcTradeTypeType - 成交类型
        ("PriceSource", ctypes.c_char),  # TThostFtdcPriceSourceType - 成交价来源
        ("TraderID", ctypes.c_char * 21),  # TThostFtdcTraderIDType - 交易所交易员代码
        ("OrderLocalID", ctypes.c_char * 13),  # TThostFtdcOrderLocalIDType - 本地报单编号
        ("ClearingPartID", ctypes.c_char * 11),  # TThostFtdcParticipantIDType - 结算会员编号
        ("BusinessUnit", ctypes.c_char * 21),  # TThostFtdcBusinessUnitType - 业务单元
        ("_padding_0", ctypes.c_char * 2),  # 对齐填充2字节
        ("SequenceNo", ctypes.c_int),  # TThostFtdcSequenceNoType - 序号
        ("TradingDay", ctypes.c_char * 9),  # TThostFtdcDateType - 交易日
        ("_padding_1", ctypes.c_char * 3),  # 对齐填充3字节
        ("SettlementID", ctypes.c_int),  # TThostFtdcSettlementIDType - 结算编号
        ("BrokerOrderSeq", ctypes.c_int),  # TThostFtdcSequenceNoType - 经纪公司报单编号
        ("TradeSource", ctypes.c_char),  # TThostFtdcTradeSourceType - 成交来源
        ("InvestUnitID", ctypes.c_char * 17),  # TThostFtdcInvestUnitIDType - 投资单元代码
        ("InstrumentID", ctypes.c_char * 81),  # TThostFtdcInstrumentIDType - 合约代码
        ("ExchangeInstID", ctypes.c_char * 81),  # TThostFtdcExchangeInstIDType - 合约在交易所的代码
    ]
    # 原始大小: 496 字节，对齐后大小: 496 字节

# TradeField 字段中文名映射
TRADEFIELD_FIELD_NAMES_CN = {
    "BrokerID": "经纪公司代码",
    "InvestorID": "投资者代码",
    "reserve1": "保留的无效字段",
    "OrderRef": "报单引用",
    "UserID": "用户代码",
    "ExchangeID": "交易所代码",
    "TradeID": "成交编号",
    "Direction": "买卖方向",
    "OrderSysID": "报单编号",
    "ParticipantID": "会员代码",
    "ClientID": "客户代码",
    "TradingRole": "交易角色",
    "reserve2": "保留的无效字段",
    "OffsetFlag": "开平标志",
    "HedgeFlag": "投机套保标志",
    "Price": "价格",
    "Volume": "数量",
    "TradeDate": "成交时期",
    "TradeTime": "成交时间",
    "TradeType": "成交类型",
    "PriceSource": "成交价来源",
    "TraderID": "交易所交易员代码",
    "OrderLocalID": "本地报单编号",
    "ClearingPartID": "结算会员编号",
    "BusinessUnit": "业务单元",
    "SequenceNo": "序号",
    "TradingDay": "交易日",
    "SettlementID": "结算编号",
    "BrokerOrderSeq": "经纪公司报单编号",
    "TradeSource": "成交来源",
    "InvestUnitID": "投资单元代码",
    "InstrumentID": "合约代码",
    "ExchangeInstID": "合约在交易所的代码",
}

# === OrderField ===
class OrderField(ctypes.Structure):
    """报单数据结构 - 对应CThostFtdcOrderField"""
    _pack_ = 1
    _fields_ = [
        ("BrokerID", ctypes.c_char * 11),  # TThostFtdcBrokerIDType - 经纪公司代码
        ("InvestorID", ctypes.c_char * 13),  # TThostFtdcInvestorIDType - 投资者代码
        ("reserve1", ctypes.c_char * 31),  # TThostFtdcOldInstrumentIDType - 保留的无效字段
        ("OrderRef", ctypes.c_char * 13),  # TThostFtdcOrderRefType - 报单引用
        ("UserID", ctypes.c_char * 16),  # TThostFtdcUserIDType - 用户代码
        ("OrderPriceType", ctypes.c_char),  # TThostFtdcOrderPriceTypeType - 报单价格条件
        ("Direction", ctypes.c_char),  # TThostFtdcDirectionType - 买卖方向
        ("CombOffsetFlag", ctypes.c_char * 5),  # TThostFtdcCombOffsetFlagType - 组合开平标志
        ("CombHedgeFlag", ctypes.c_char * 5),  # TThostFtdcCombHedgeFlagType - 组合投机套保标志
        ("LimitPrice", ctypes.c_double),  # TThostFtdcPriceType - 价格
        ("VolumeTotalOriginal", ctypes.c_int),  # TThostFtdcVolumeType - 数量
        ("TimeCondition", ctypes.c_char),  # TThostFtdcTimeConditionType - 有效期类型
        ("GTDDate", ctypes.c_char * 9),  # TThostFtdcDateType - 日期
        ("VolumeCondition", ctypes.c_char),  # TThostFtdcVolumeConditionType - 成交量类型
        ("_padding_0", ctypes.c_char * 1),  # 对齐填充1字节
        ("MinVolume", ctypes.c_int),  # TThostFtdcVolumeType - 最小成交量
        ("ContingentCondition", ctypes.c_char),  # TThostFtdcContingentConditionType - 触发条件
        ("_padding_1", ctypes.c_char * 3),  # 对齐填充3字节
        ("StopPrice", ctypes.c_double),  # TThostFtdcPriceType - 止损价
        ("ForceCloseReason", ctypes.c_char),  # TThostFtdcForceCloseReasonType - 强平原因
        ("_padding_2", ctypes.c_char * 3),  # 对齐填充3字节
        ("IsAutoSuspend", ctypes.c_int),  # TThostFtdcBoolType - 自动挂起标志
        ("BusinessUnit", ctypes.c_char * 21),  # TThostFtdcBusinessUnitType - 业务单元
        ("_padding_3", ctypes.c_char * 3),  # 对齐填充3字节
        ("RequestID", ctypes.c_int),  # TThostFtdcRequestIDType - 请求编号
        ("OrderLocalID", ctypes.c_char * 13),  # TThostFtdcOrderLocalIDType - 本地报单编号
        ("ExchangeID", ctypes.c_char * 9),  # TThostFtdcExchangeIDType - 交易所代码
        ("ParticipantID", ctypes.c_char * 11),  # TThostFtdcParticipantIDType - 会员代码
        ("ClientID", ctypes.c_char * 11),  # TThostFtdcClientIDType - 客户代码
        ("reserve2", ctypes.c_char * 31),  # TThostFtdcOldExchangeInstIDType - 保留的无效字段
        ("TraderID", ctypes.c_char * 21),  # TThostFtdcTraderIDType - 交易所交易员代码
        ("InstallID", ctypes.c_int),  # TThostFtdcInstallIDType - 安装编号
        ("OrderSubmitStatus", ctypes.c_char),  # TThostFtdcOrderSubmitStatusType - 报单提交状态
        ("_padding_4", ctypes.c_char * 3),  # 对齐填充3字节
        ("NotifySequence", ctypes.c_int),  # TThostFtdcSequenceNoType - 报单提示序号
        ("TradingDay", ctypes.c_char * 9),  # TThostFtdcDateType - 交易日
        ("_padding_5", ctypes.c_char * 3),  # 对齐填充3字节
        ("SettlementID", ctypes.c_int),  # TThostFtdcSettlementIDType - 结算编号
        ("OrderSysID", ctypes.c_char * 21),  # TThostFtdcOrderSysIDType - 报单编号
        ("OrderSource", ctypes.c_char),  # TThostFtdcOrderSourceType - 报单来源
        ("OrderStatus", ctypes.c_char),  # TThostFtdcOrderStatusType - 报单状态
        ("OrderType", ctypes.c_char),  # TThostFtdcOrderTypeType - 报单类型
        ("VolumeTraded", ctypes.c_int),  # TThostFtdcVolumeType - 今成交数量
        ("VolumeTotal", ctypes.c_int),  # TThostFtdcVolumeType - 剩余数量
        ("InsertDate", ctypes.c_char * 9),  # TThostFtdcDateType - 报单日期
        ("InsertTime", ctypes.c_char * 9),  # TThostFtdcTimeType - 委托时间
        ("ActiveTime", ctypes.c_char * 9),  # TThostFtdcTimeType - 激活时间
        ("SuspendTime", ctypes.c_char * 9),  # TThostFtdcTimeType - 挂起时间
        ("UpdateTime", ctypes.c_char * 9),  # TThostFtdcTimeType - 最后修改时间
        ("CancelTime", ctypes.c_char * 9),  # TThostFtdcTimeType - 撤销时间
        ("ActiveTraderID", ctypes.c_char * 21),  # TThostFtdcTraderIDType - 最后修改交易所交易员代码
        ("ClearingPartID", ctypes.c_char * 11),  # TThostFtdcParticipantIDType - 结算会员编号
        ("_padding_6", ctypes.c_char * 2),  # 对齐填充2字节
        ("SequenceNo", ctypes.c_int),  # TThostFtdcSequenceNoType - 序号
        ("FrontID", ctypes.c_int),  # TThostFtdcFrontIDType - 前置编号
        ("SessionID", ctypes.c_int),  # TThostFtdcSessionIDType - 会话编号
        ("UserProductInfo", ctypes.c_char * 11),  # TThostFtdcProductInfoType - 用户端产品信息
        ("StatusMsg", ctypes.c_char * 81),  # TThostFtdcErrorMsgType - 状态信息
        ("UserForceClose", ctypes.c_int),  # TThostFtdcBoolType - 用户强评标志
        ("ActiveUserID", ctypes.c_char * 16),  # TThostFtdcUserIDType - 操作用户代码
        ("BrokerOrderSeq", ctypes.c_int),  # TThostFtdcSequenceNoType - 经纪公司报单编号
        ("RelativeOrderSysID", ctypes.c_char * 21),  # TThostFtdcOrderSysIDType - 相关报单
        ("_padding_7", ctypes.c_char * 3),  # 对齐填充3字节
        ("ZCETotalTradedVolume", ctypes.c_int),  # TThostFtdcVolumeType - 郑商所成交数量
        ("IsSwapOrder", ctypes.c_int),  # TThostFtdcBoolType - 互换单标志
        ("BranchID", ctypes.c_char * 9),  # TThostFtdcBranchIDType - 营业部编号
        ("InvestUnitID", ctypes.c_char * 17),  # TThostFtdcInvestUnitIDType - 投资单元代码
        ("AccountID", ctypes.c_char * 13),  # TThostFtdcAccountIDType - 资金账号
        ("CurrencyID", ctypes.c_char * 4),  # TThostFtdcCurrencyIDType - 币种代码
        ("reserve3", ctypes.c_char * 16),  # TThostFtdcOldIPAddressType - 保留的无效字段
        ("MacAddress", ctypes.c_char * 21),  # TThostFtdcMacAddressType - 地址
        ("InstrumentID", ctypes.c_char * 81),  # TThostFtdcInstrumentIDType - 合约代码
        ("ExchangeInstID", ctypes.c_char * 81),  # TThostFtdcExchangeInstIDType - 合约在交易所的代码
        ("IPAddress", ctypes.c_char * 33),  # TThostFtdcIPAddressType - 地址
        ("_padding_final", ctypes.c_char * 5),  # 结构体末尾填充5字节
    ]
    # 原始大小: 851 字节，对齐后大小: 856 字节

# OrderField 字段中文名映射
ORDERFIELD_FIELD_NAMES_CN = {
    "BrokerID": "经纪公司代码",
    "InvestorID": "投资者代码",
    "reserve1": "保留的无效字段",
    "OrderRef": "报单引用",
    "UserID": "用户代码",
    "OrderPriceType": "报单价格条件",
    "Direction": "买卖方向",
    "CombOffsetFlag": "组合开平标志",
    "CombHedgeFlag": "组合投机套保标志",
    "LimitPrice": "价格",
    "VolumeTotalOriginal": "数量",
    "TimeCondition": "有效期类型",
    "GTDDate": "日期",
    "VolumeCondition": "成交量类型",
    "MinVolume": "最小成交量",
    "ContingentCondition": "触发条件",
    "StopPrice": "止损价",
    "ForceCloseReason": "强平原因",
    "IsAutoSuspend": "自动挂起标志",
    "BusinessUnit": "业务单元",
    "RequestID": "请求编号",
    "OrderLocalID": "本地报单编号",
    "ExchangeID": "交易所代码",
    "ParticipantID": "会员代码",
    "ClientID": "客户代码",
    "reserve2": "保留的无效字段",
    "TraderID": "交易所交易员代码",
    "InstallID": "安装编号",
    "OrderSubmitStatus": "报单提交状态",
    "NotifySequence": "报单提示序号",
    "TradingDay": "交易日",
    "SettlementID": "结算编号",
    "OrderSysID": "报单编号",
    "OrderSource": "报单来源",
    "OrderStatus": "报单状态",
    "OrderType": "报单类型",
    "VolumeTraded": "今成交数量",
    "VolumeTotal": "剩余数量",
    "InsertDate": "报单日期",
    "InsertTime": "委托时间",
    "ActiveTime": "激活时间",
    "SuspendTime": "挂起时间",
    "UpdateTime": "最后修改时间",
    "CancelTime": "撤销时间",
    "ActiveTraderID": "最后修改交易所交易员代码",
    "ClearingPartID": "结算会员编号",
    "SequenceNo": "序号",
    "FrontID": "前置编号",
    "SessionID": "会话编号",
    "UserProductInfo": "用户端产品信息",
    "StatusMsg": "状态信息",
    "UserForceClose": "用户强评标志",
    "ActiveUserID": "操作用户代码",
    "BrokerOrderSeq": "经纪公司报单编号",
    "RelativeOrderSysID": "相关报单",
    "ZCETotalTradedVolume": "郑商所成交数量",
    "IsSwapOrder": "互换单标志",
    "BranchID": "营业部编号",
    "InvestUnitID": "投资单元代码",
    "AccountID": "资金账号",
    "CurrencyID": "币种代码",
    "reserve3": "保留的无效字段",
    "MacAddress": "地址",
    "InstrumentID": "合约代码",
    "ExchangeInstID": "合约在交易所的代码",
    "IPAddress": "地址",
}

# === SettlementInfoConfirmField ===
class SettlementInfoConfirmField(ctypes.Structure):
    """投资者结算结果数据结构 - 对应CThostFtdcSettlementInfoConfirmField"""
    _pack_ = 1
    _fields_ = [
        ("BrokerID", ctypes.c_char * 11),  # TThostFtdcBrokerIDType - 经纪公司代码
        ("InvestorID", ctypes.c_char * 13),  # TThostFtdcInvestorIDType - 投资者代码
        ("ConfirmDate", ctypes.c_char * 9),  # TThostFtdcDateType - 确认日期
        ("ConfirmTime", ctypes.c_char * 9),  # TThostFtdcTimeType - 确认时间
        ("_padding_0", ctypes.c_char * 2),  # 对齐填充2字节
        ("SettlementID", ctypes.c_int),  # TThostFtdcSettlementIDType - 结算编号
        ("AccountID", ctypes.c_char * 13),  # TThostFtdcAccountIDType - 投资者帐号
        ("CurrencyID", ctypes.c_char * 4),  # TThostFtdcCurrencyIDType - 币种代码
        ("_padding_final", ctypes.c_char * 3),  # 结构体末尾填充3字节
    ]
    # 原始大小: 65 字节，对齐后大小: 68 字节

# SettlementInfoConfirmField 字段中文名映射
SETTLEMENTINFOCONFIRMFIELD_FIELD_NAMES_CN = {
    "BrokerID": "经纪公司代码",
    "InvestorID": "投资者代码",
    "ConfirmDate": "确认日期",
    "ConfirmTime": "确认时间",
    "SettlementID": "结算编号",
    "AccountID": "投资者帐号",
    "CurrencyID": "币种代码",
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货测试案例实现
基于cases/futures_test_cases.csv中定义的测试案例
"""

import pytest
import time
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, os.path.join(parent_dir, 'trader'))

from trader.ctp_direct_wrapper import CTPDirectWrapper


class TestFuturesCases:
    """期货测试案例类"""
    
    @pytest.fixture(scope="class")
    def ctp_service(self):
        """CTP服务fixture"""
        service = CTPDirectWrapper()
        
        # 初始化服务
        init_result = service.initialize()
        assert init_result, "CTP服务初始化失败"
        
        # 等待连接和登录完成
        login_success = False
        for i in range(15):  # 增加等待时间
            if service.is_connected() and service.is_logged_in():
                login_success = True
                break
            time.sleep(1)
        
        assert login_success, "CTP服务连接或登录失败"
        print(f"✓ CTP服务初始化、连接和登录成功")
        
        yield service
        
        # 清理：平掉所有测试持仓
        self._cleanup_positions(service)
    
    def _cleanup_positions(self, service):
        """清理测试持仓"""
        positions = service.query_investor_position()
        if positions.get('success') and positions.get('data'):
            for pos in positions['data']:
                instrument_id = pos.get('InstrumentID')
                position_volume = pos.get('Position', 0)
                direction = pos.get('PosiDirection')
                
                if position_volume > 0:
                    if direction == '2':  # 多头持仓
                        service.order_insert(
                            instrument_id=instrument_id,
                            direction="卖",
                            offset_flag="平仓",
                            price=4000.0 * 0.95,  # 使用固定价格
                            volume=position_volume
                        )
                    elif direction == '3':  # 空头持仓
                        service.order_insert(
                            instrument_id=instrument_id,
                            direction="买",
                            offset_flag="平仓",
                            price=4000.0 * 1.05,  # 使用固定价格
                            volume=position_volume
                        )
    
    def test_tc_fu001_stock_index_futures_buy_open(self, ctp_service):
        """TC_FU001: 验证股指期货买入开仓功能"""
        service = ctp_service
        
        # STEP_001: 查询账户资金
        account_result = service.query_trading_account()
        assert account_result.get('success'), "账户查询失败"
        
        account_data_list = account_result.get('data', [])
        assert len(account_data_list) > 0, "账户数据不能为空"
        account_data = account_data_list[0]
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        assert available_fund >= 50000, f"可用资金{available_fund}不足50000"
        print(f"✓ 账户可用资金: {available_fund}")
        
        # STEP_003: 查询当前持仓
        before_position = service.query_investor_position(instrument_id="IF2512")
        assert before_position.get('success'), "持仓查询失败"
        
        before_long_volume = 0
        for pos in before_position.get('data', []):
            if pos.get('InstrumentID') == 'IF2512' and pos.get('PosiDirection') == '2':  # 多头
                before_long_volume = pos.get('Position', 0)
                break
        print(f"✓ 开仓前多头持仓: {before_long_volume}手")
        
        # STEP_006: 验证开仓结果
        after_position = service.query_investor_position(instrument_id="IF2512")
        assert after_position.get('success'), "开仓后持仓查询失败"
        
        after_long_volume = 0
        for pos in after_position.get('data', []):
            if pos.get('InstrumentID') == 'IF2512' and pos.get('PosiDirection') == '2':  # 多头
                after_long_volume = pos.get('Position', 0)
                break
        
        # 验证保证金占用
        after_account = service.query_trading_account()
        after_account_data = after_account.get('data', [{}])[0]
        after_available = after_account_data.get('Available', 0)
        margin_used = available_fund - after_available
        assert margin_used > 0, "应该有保证金占用"
        
        print(f"✓ 开仓成功，持仓增加到{after_long_volume}手，保证金占用{margin_used}")
    
    def test_tc_fu002_stock_index_futures_sell_close(self, ctp_service):
        """TC_FU002: 验证股指期货卖出平仓功能"""
        service = ctp_service
        
        # 先确保有多头持仓
        self._ensure_long_position(service, "IF2512", 1)
        
        # STEP_001: 查询持仓状态
        before_position = service.query_investor_position(instrument_id="IF2512")
        assert before_position.get('success'), "持仓查询失败"
        
        before_long_volume = 0
        for pos in before_position.get('data', []):
            if pos.get('InstrumentID') == 'IF2512' and pos.get('PosiDirection') == '2':  # 多头
                before_long_volume = pos.get('Position', 0)
                break
        
        assert before_long_volume > 0, "需要有多头持仓才能平仓"
        print(f"✓ 平仓前多头持仓: {before_long_volume}手")
        
        # STEP_002: 计算平仓保证金释放
        before_account = service.query_trading_account()
        before_account_data = before_account.get('data', [{}])[0]
        before_available = before_account_data.get('Available', 0)
        
        # STEP_003: 提交卖出平仓订单
        close_result = service.order_insert(
            instrument_id="IF2512",
            exchange_id="CFFEX",
            direction="卖",
            price=4000,
            offset_flag="平仓",
            volume=1
        )
        assert close_result is not None, "平仓订单提交失败"
        print(f"✓ 卖出平仓订单提交成功")
        
        # 等待成交
        time.sleep(3)
        
        # STEP_004: 验证平仓结果
        after_position = service.query_investor_position(instrument_id="IF2512")
        assert after_position.get('success'), "平仓后持仓查询失败"
        
        after_long_volume = 0
        for pos in after_position.get('data', []):
            if pos.get('InstrumentID') == 'IF2512' and pos.get('PosiDirection') == '2':  # 多头
                after_long_volume = pos.get('Position', 0)
                break
        
        # 验证持仓减少
        assert after_long_volume == before_long_volume - 1, f"持仓应减少1手，实际从{before_long_volume}变为{after_long_volume}"
        
        # 验证保证金释放
        after_account = service.query_trading_account()
        after_account_data = after_account.get('data', [{}])[0]
        after_available = after_account_data.get('Available', 0)
        margin_released = after_available - before_available
        assert margin_released > 0, "应该有保证金释放"
        
        # 验证盈亏结算
        trades = service.query_trade()
        assert trades.get('success'), "成交查询失败"
        
        print(f"✓ 平仓成功，持仓减少到{after_long_volume}手，保证金释放{margin_released}")
    
    def test_tc_fu003_stock_index_futures_close_today(self, ctp_service):
        """TC_FU003: 验证股指期货平今仓功能"""
        service = ctp_service
        
        # 先开仓建立当日持仓
        open_result = service.order_insert(
            instrument_id="IF2512",
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=4000.0,
            volume=1
        )
        assert open_result is not None, "开仓订单提交失败"
        time.sleep(3)
        
        # 查询当日持仓
        position_detail = service.query_investor_position(instrument_id="IF2512")
        assert position_detail.get('success'), "持仓明细查询失败"
        
        today_volume = 0
        for detail in position_detail.get('data', []):
            if detail.get('InstrumentID') == 'IF2512' and detail.get('PosiDirection') == '0':  # 多头
                today_volume += detail.get('Volume', 0)
        
        assert today_volume > 0, "需要有当日持仓才能平今"
        print(f"✓ 当日持仓: {today_volume}手")
        
        # 执行平今仓操作
        close_today_result = service.order_insert(
            instrument_id="IF2512",
            exchange_id="CFFEX",
            direction="卖",
            offset_flag="平今",
            price=4050.0,
            volume=1
        )
        assert close_today_result is not None, "平今仓订单提交失败"
        print(f"✓ 平今仓订单提交成功")
        
        time.sleep(3)
        
        # 验证平今仓结果
        trades = service.query_trade()
        assert trades.get('success'), "成交查询失败"
        
        # 检查是否有平今成交记录
        close_today_trades = [t for t in trades.get('data', []) 
                             if t.get('InstrumentID') == 'IF2512' and t.get('OffsetFlag') == '3']  # 平今
        assert len(close_today_trades) > 0, "应该有平今成交记录"
        
        print(f"✓ 平今仓成功，手续费按平今标准收取")

    def test_tc_fu004_insufficient_margin_restriction(self, ctp_service):
        """TC_FU004: 验证保证金不足时的开仓限制"""
        service = ctp_service
        
        # STEP_001: 查询当前资金状况
        account_result = service.query_trading_account()
        assert account_result.get('success'), "账户查询失败"
        
        account_data_list = account_result.get('data', [])
        account_data = account_data_list[0] if account_data_list else {}
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        print(f"✓ 当前可用资金: {available_fund}")
        
        # STEP_002: 计算大额开仓保证金需求
        large_volume = 10
        contract_price = 4000.0
        required_margin = contract_price * 300 * large_volume * 0.12  # 约480万
        
        print(f"✓ 开仓{large_volume}手需要保证金: {required_margin}")
        
        # STEP_003: 如果资金充足，先消耗资金到不足状态
        if available_fund >= required_margin:
            # 通过开仓消耗资金
            consume_volume = int(available_fund / (contract_price * 300 * 0.12)) - 1
            if consume_volume > 0:
                service.order_insert(
                    instrument_id="IF2512",
                    direction="买",
                    offset_flag="开仓",
                    price=contract_price,
                    volume=min(consume_volume, 5)  # 限制最大消耗量
                )
                time.sleep(2)
        
        # STEP_004: 尝试大额开仓
        large_order_result = service.order_insert(
            instrument_id="IF2512",
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=4000.0,
            volume=large_volume
        )
        
        # STEP_005: 验证风控提示
        # 检查订单是否被拒绝或查询订单状态
        time.sleep(2)
        orders = service.query_order()
        
        if orders.get('success') and orders.get('data'):
            latest_order = orders['data'][-1]  # 最新订单
            order_status = latest_order.get('order_status', '')
            
            # 验证订单被拒绝或资金不足
            if order_status in ['已拒绝', '错误']:
                print(f"✓ 大额开仓被正确拒绝，订单状态: {order_status}")
            else:
                # 检查是否因资金不足未能完全成交
                filled_volume = latest_order.get('volume_traded', 0)
                if filled_volume < large_volume:
                    print(f"✓ 大额开仓部分成交，成交{filled_volume}手，剩余{large_volume-filled_volume}手因资金不足")
        
        # 验证账户状态
        final_account = service.query_trading_account()
        final_account_data = final_account.get('data', [{}])[0]
        final_available = final_account_data.get('Available', 0)  # 使用正确字段名
        print(f"✓ 最终可用资金: {final_available}")
    
    def test_tc_fu005_commodity_futures_open(self, ctp_service):
        """TC_FU005: 验证商品期货开仓功能"""
        service = ctp_service
        
        # STEP_001: 查询账户资金
        account_result = service.query_trading_account()
        assert account_result.get('success'), "账户查询失败"
        
        account_data_list = account_result.get('data', [])
        account_data = account_data_list[0] if account_data_list else {}
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        assert available_fund >= 20000, f"可用资金{available_fund}不足20000"
        print(f"✓ 账户可用资金: {available_fund}")
        
        # STEP_002: 查询螺纹钢合约
        instrument_result = service.query_instrument(
            instrument_id="rb2512",
            exchange_id="SHFE"
        )
        assert instrument_result.get('success'), "螺纹钢合约查询失败"
        
        # STEP_003: 计算商品期货保证金
        volume = 10
        contract_price = 3500.0
        estimated_margin = contract_price * 10 * volume * 0.08  # rb合约乘数10，保证金率约8%
        assert available_fund >= estimated_margin, f"保证金不足，需要{estimated_margin}"
        print(f"✓ 预估保证金: {estimated_margin}")
        
        # STEP_004: 提交商品期货开仓
        before_position = service.query_investor_position(instrument_id="rb2512")
        before_volume = 0
        if before_position.get('success'):
            for pos in before_position.get('data', []):
                if pos.get('InstrumentID') == 'rb2512' and pos.get('PosiDirection') == '2':
                    before_volume = pos.get('Position', 0)
                    break
        
        order_result = service.order_insert(
            instrument_id="rb2512",
            exchange_id="SHFE",
            direction="买",
            offset_flag="开仓",
            price=3500.0,
            volume=volume
        )
        assert order_result is not None, "商品期货订单提交失败"
        print(f"✓ 螺纹钢买入开仓{volume}手订单提交成功")
        
        # 等待成交
        time.sleep(3)
        
        # STEP_005: 验证商品期货开仓
        after_position = service.query_investor_position(instrument_id="rb2512")
        assert after_position.get('success'), "开仓后持仓查询失败"
        
        after_volume = 0
        for pos in after_position.get('data', []):
            if pos.get('InstrumentID') == 'rb2512' and pos.get('PosiDirection') == '2':
                after_volume = pos.get('Position', 0)
                break
        
        # 验证持仓增加
        position_increase = after_volume - before_volume
        assert position_increase > 0, f"持仓应该增加，实际从{before_volume}变为{after_volume}"
        
        # 验证保证金计算正确
        after_account = service.query_trading_account()
        after_account_data = after_account.get('data', [{}])[0]
        margin_used = available_fund - after_account_data.get('Available', 0)  # 使用正确字段名
        
        print(f"✓ 商品期货开仓成功，持仓增加{position_increase}手，保证金占用{margin_used}")

    def test_tc_fu006_delivery_month_restriction(self, ctp_service):
        """TC_FU006: 验证交割月的交易限制"""
        service = ctp_service
        
        # 查询临近交割的合约（假设使用当月合约）
        current_month_contract = "IF2412"  # 假设12月为交割月
        
        instrument_result = service.query_instrument(
            instrument_id=current_month_contract,
            exchange_id="CFFEX"
        )
        
        if not instrument_result.get('success'):
            pytest.skip(f"合约{current_month_contract}不存在，跳过测试")
        
        # 尝试开仓交割月合约
        order_result = service.order_insert(
            instrument_id=current_month_contract,
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=4000.0,
            volume=1
        )
        
        # 检查是否被限制
        time.sleep(2)
        orders = service.query_order()
        
        if orders.get('success') and orders.get('data'):
            latest_order = orders['data'][-1]
            order_status = latest_order.get('OrderStatus', '')
            
            # 个人投资者应该被拒绝
            if order_status in ['已拒绝', '错误']:
                print(f"✓ 交割月限制生效，个人投资者开仓被拒绝")
            else:
                print(f"⚠ 交割月合约开仓成功，可能为机构投资者账户")

    def test_tc_fu008_treasury_futures_trading(self, ctp_service):
        """TC_FU008: 验证国债期货交易功能"""
        service = ctp_service
        
        # 查询国债期货合约
        treasury_contract = "T2512"
        instrument_result = service.query_instrument(
            instrument_id=treasury_contract,
            exchange_id="CFFEX"
        )
        
        if not instrument_result.get('success'):
            pytest.skip(f"国债期货合约{treasury_contract}不存在，跳过测试")
        
        # 查询账户资金
        account_result = service.query_trading_account()
        account_data = account_result.get('data', [{}])[0]
        available_fund = account_data.get('Available', 0)
        
        # 估算国债期货保证金需求
        estimated_margin = 100 * 10000 * 1 * 0.02  # 假设面值100万，保证金率2%
        
        if available_fund < estimated_margin:
            pytest.skip(f"资金不足，需要{estimated_margin}，可用{available_fund}")
        
        # 执行国债期货开仓
        order_result = service.order_insert(
            instrument_id=treasury_contract,
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=98.5,  # 国债期货净价报价
            volume=1
        )
        
        assert order_result is not None, "国债期货订单提交失败"
        print(f"✓ 国债期货开仓订单提交成功，使用净价报价")
        
        time.sleep(3)
        
        # 验证持仓建立
        position_result = service.query_investor_position(instrument_id=treasury_contract)
        assert position_result.get('success'), "国债期货持仓查询失败"
        
        print(f"✓ 国债期货交易成功，价格按净价报价方式")

    def test_tc_fu010_option_buy_open(self, ctp_service):
        """TC_FU010: 验证期权买入开仓功能"""
        service = ctp_service
        
        # 查询期权合约
        option_contract = "IO2512-C-4000"  # 假设的期权合约
        instrument_result = service.query_instrument(
            instrument_id=option_contract,
            exchange_id="CFFEX"
        )
        
        if not instrument_result.get('success'):
            pytest.skip(f"期权合约{option_contract}不存在，跳过测试")
        
        # 查询账户资金
        account_result = service.query_trading_account()
        account_data = account_result.get('data', [{}])[0]
        available_fund = account_data.get('Available', 0)
        
        # 估算权利金成本
        premium_cost = 50.0 * 100 * 1  # 假设权利金50点，乘数100
        
        if available_fund < premium_cost:
            pytest.skip(f"资金不足支付权利金，需要{premium_cost}，可用{available_fund}")
        
        # 执行期权买入开仓
        before_account = service.query_trading_account()
        before_available = before_account.get('data', [{}])[0].get('Available', 0)
        
        order_result = service.order_insert(
            instrument_id=option_contract,
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=50.0,
            volume=1
        )
        
        assert order_result is not None, "期权买入开仓订单提交失败"
        print(f"✓ 期权买入开仓订单提交成功")
        
        time.sleep(3)
        
        # 验证权利金扣除
        after_account = service.query_trading_account()
        after_available = after_account.get('data', [{}])[0].get('Available', 0)
        premium_paid = before_available - after_available
        
        assert premium_paid > 0, "应该扣除权利金"
        print(f"✓ 期权开仓成功，权利金扣除{premium_paid}，获得期权权利")

    def test_tc_fu013_nonexistent_contract(self, ctp_service):
        """TC_FU013: 验证期货合约不存在"""
        service = ctp_service
        
        # 使用不存在的合约代码
        fake_contract = "FAKE9999"
        
        # 尝试查询不存在的合约
        instrument_result = service.query_instrument(
            instrument_id=fake_contract,
            exchange_id="CFFEX"
        )
        print(instrument_result)
        # 验证查询失败或返回空数据
        if instrument_result.get('success'):
            data = instrument_result.get('data', [])
            assert len(data) == 0, "不存在的合约应该返回空数据"
        
        # 尝试交易不存在的合约
        order_result = service.order_insert(
            instrument_id=fake_contract,
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=1000.0,
            volume=1
        )
        
        # 检查订单状态
        time.sleep(2)
        orders = service.query_order()
        
        if orders.get('success') and orders.get('data'):
            latest_order = orders['data'][-1]
            order_status = latest_order.get('OrderStatus', '')
            
            assert order_status in ['已拒绝', '错误'], "不存在合约的订单应该被拒绝"
            print(f"✓ 系统正确提示'期货合约不存在'，订单被拒绝")

    def test_tc_fu015_price_limit_restriction(self, ctp_service):
        """TC_FU015: 验证期货价格超出涨跌停限制"""
        service = ctp_service
        
        # 查询合约信息获取涨跌停价格
        instrument_result = service.query_instrument(
            instrument_id="IF2512",
            exchange_id="CFFEX"
        )
        assert instrument_result.get('success'), "合约查询失败"
        
        instrument_data = instrument_result.get('data', [{}])[0]
        upper_limit = instrument_data.get('UpperLimitPrice', 0)
        lower_limit = instrument_data.get('LowerLimitPrice', 0)
        
        if upper_limit == 0:
            pytest.skip("无法获取涨跌停价格，跳过测试")
        
        print(f"✓ 涨停价: {upper_limit}, 跌停价: {lower_limit}")
        
        # 尝试以超出涨停的价格买入
        over_limit_price = upper_limit + 100
        order_result = service.order_insert(
            instrument_id="IF2512",
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=over_limit_price,
            volume=1
        )
        
        # 检查订单状态
        time.sleep(2)
        orders = service.query_order()
        
        if orders.get('success') and orders.get('data'):
            latest_order = orders['data'][-1]
            order_status = latest_order.get('OrderStatus', '')
            
            assert order_status in ['已拒绝', '错误'], "超出涨跌停的订单应该被拒绝"
            print(f"✓ 系统正确提示'委托价格超出涨跌停限制'，订单被拒绝")

    def test_tc_fu016_close_volume_exceeds_position(self, ctp_service):
        """TC_FU016: 验证期货平仓数量超出持仓"""
        service = ctp_service
        
        # 查询当前持仓
        position_result = service.query_investor_position(instrument_id="IF2512")
        assert position_result.get('success'), "持仓查询失败"
        
        current_volume = 0
        for pos in position_result.get('data', []):
            if pos.get('InstrumentID') == 'IF2512' and pos.get('PosiDirection') == '2':  # 多头
                current_volume = pos.get('Position', 0)
                break
        
        print(f"✓ 当前多头持仓: {current_volume}手")
        
        # 尝试平仓超出持仓数量
        excessive_volume = current_volume + 5
        order_result = service.order_insert(
            instrument_id="IF2512",
            exchange_id="CFFEX",
            direction="卖",
            offset_flag="平仓",
            price=4000.0,
            volume=excessive_volume
        )
        
        # 检查订单状态
        time.sleep(2)
        orders = service.query_order()
        
        if orders.get('success') and orders.get('data'):
            latest_order = orders['data'][-1]
            order_status = latest_order.get('OrderStatus', '')
            volume_traded = latest_order.get('VolumeTraded', 0)
            
            # 验证系统限制
            if order_status in ['已拒绝', '错误']:
                print(f"✓ 系统正确提示'平仓数量超出持仓'，订单被拒绝")
            elif volume_traded < excessive_volume:
                print(f"✓ 系统限制平仓数量，实际成交{volume_traded}手")

    def test_tc_fu101_margin_sufficiency_verification(self, ctp_service):
        """TC_FU101: 验证开仓保证金充足性"""
        service = ctp_service
        
        # STEP_001: 查询保证金状态
        account_result = service.query_trading_account()
        assert account_result.get('success'), "账户查询失败"

        account_data_list = account_result.get('data', [])
        account_data = account_data_list[0] if account_data_list else {}
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        margin_used = account_data.get('CurrMargin', 0)  # 使用正确字段名
        
        print(f"✓ 可用资金: {available_fund}, 已用保证金: {margin_used}")
        
        # STEP_002: 计算所需保证金
        volume = 2
        contract_price = 4000.0
        required_margin = contract_price * 300 * volume * 0.12  # 约96万
        
        print(f"✓ 开仓{volume}手需要保证金: {required_margin}")
        
        # STEP_003: 判断保证金是否充足
        if available_fund >= required_margin:
            # 资金充足，应该能开仓
            order_result = service.order_insert(
                instrument_id="IF2512",
                direction="买",
                offset_flag="开仓",
                price=contract_price,
                volume=volume
            )
            assert order_result is not None, "资金充足时开仓应该成功"
            print(f"✓ 保证金充足，开仓成功")
            
        else:
            # 资金不足，应该被拒绝
            order_result = service.order_insert(
                instrument_id="IF2512",
                direction="买",
                offset_flag="开仓",
                price=contract_price,
                volume=volume
            )
            
            # 检查订单状态
            time.sleep(2)
            orders = service.query_order()
            if orders.get('success') and orders.get('data'):
                latest_order = orders['data'][-1]
                order_status = latest_order.get('order_status', '')
                print(f"✓ 保证金不足，订单状态: {order_status}")

    def test_tc_fu102_maintenance_margin_monitoring(self, ctp_service):
        """TC_FU102: 验证维持保证金监控"""
        service = ctp_service
        
        # STEP_001: 建立测试持仓
        self._ensure_long_position(service, "IF2512", 1)
        
        # STEP_002: 查询持仓保证金
        position_result = service.query_investor_position(instrument_id="IF2512")
        assert position_result.get('success'), "持仓查询失败"
        
        position_margin = 0
        for pos in position_result.get('data', []):
            if pos.get('InstrumentID') == 'IF2512':  # 使用正确字段名
                position_margin = pos.get('UseMargin', 0)  # 使用正确字段名
                break
        
        assert position_margin > 0, "应该有保证金占用"
        print(f"✓ 持仓保证金占用: {position_margin}")
        
        # STEP_003: 查询账户保证金状态
        account_result = service.query_trading_account()
        account_data_list = account_result.get('data', [])
        account_data = account_data_list[0] if account_data_list else {}

        total_margin = account_data.get('CurrMargin', 0)  # 使用正确字段名
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        total_asset = account_data.get('Balance', 0)  # 使用正确字段名
        
        # STEP_004: 计算维持保证金比率
        if total_asset > 0:
            margin_ratio = total_margin / total_asset
            print(f"✓ 保证金比率: {margin_ratio:.2%}")
            
            # 检查是否接近维持保证金要求
            if margin_ratio > 0.8:  # 假设80%为警戒线
                print(f"⚠ 保证金比率较高，接近风控线")
            else:
                print(f"✓ 保证金比率正常")
        
        # STEP_005: 模拟价格变动影响（通过查询浮动盈亏）
        position_detail = service.query_investor_position(instrument_id="IF2512")
        if position_detail.get('success') and position_detail.get('data'):
            for detail in position_detail['data']:
                unrealized_pnl = detail.get('PositionProfit', 0)  # 使用正确字段名
                print(f"✓ 持仓浮动盈亏: {unrealized_pnl}")

                if unrealized_pnl < -position_margin * 0.5:  # 亏损超过保证金50%
                    print(f"⚠ 浮动亏损较大，可能触发风控")

    def _ensure_long_position(self, service, instrument_id, volume):
        """确保有指定的多头持仓"""
        position_result = service.query_investor_position(instrument_id=instrument_id)
        
        current_volume = 0
        if position_result.get('success'):
            for pos in position_result.get('data', []):
                if pos.get('InstrumentID') == instrument_id and pos.get('PosiDirection') == '2':
                    current_volume = pos.get('Position', 0)
                    break
        
        if current_volume < volume:
            need_volume = volume - current_volume
            service.order_insert(
                instrument_id=instrument_id,
                direction="买",
                offset_flag="开仓",
                price=4000.0,
                volume=need_volume
            )
            time.sleep(3)  # 等待成交

    def test_tc_fu103_single_order_volume_limit(self, ctp_service):
        """TC_FU103: 验证单笔开仓数量限制"""
        service = ctp_service
        
        # 尝试单笔开仓大量合约
        large_volume = 1000  # 假设超出单笔限制
        
        order_result = service.order_insert(
            instrument_id="IF2512",
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=4000.0,
            volume=large_volume
        )
        
        # 检查订单状态
        time.sleep(2)
        orders = service.query_order()
        
        if orders.get('success') and orders.get('data'):
            latest_order = orders['data'][-1]
            order_status = latest_order.get('OrderStatus', '')
            
            if order_status in ['已拒绝', '错误']:
                print(f"✓ 系统正确提示'单笔开仓数量超出限制'，建议分批开仓")
            else:
                print(f"⚠ 大额订单被接受，可能未触发单笔限制")

    def test_tc_fu109_trading_time_restriction(self, ctp_service):
        """TC_FU109: 验证交易时间限制"""
        service = ctp_service
        
        # 获取当前时间
        import datetime
        current_time = datetime.datetime.now().time()
        
        # 判断是否在交易时间内
        # 期货交易时间：9:30-11:30, 13:00-15:00, 21:00-23:00, 23:30-02:30
        trading_hours = [
            (datetime.time(9, 30), datetime.time(11, 30)),
            (datetime.time(13, 0), datetime.time(15, 0)),
            (datetime.time(21, 0), datetime.time(23, 0)),
        ]
        
        is_trading_time = any(start <= current_time <= end for start, end in trading_hours)
        
        # 提交订单
        order_result = service.order_insert(
            instrument_id="IF2512",
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price=4000.0,
            volume=1
        )
        
        time.sleep(2)
        orders = service.query_order()
        
        if orders.get('success') and orders.get('data'):
            latest_order = orders['data'][-1]
            order_status = latest_order.get('OrderStatus', '')
            
            if not is_trading_time:
                # 非交易时间应该被拒绝或进入预埋
                if order_status in ['已拒绝', '错误', '预埋']:
                    print(f"✓ 非交易时间订单处理正确: {order_status}")
                else:
                    print(f"⚠ 非交易时间订单状态异常: {order_status}")
            else:
                print(f"✓ 交易时间内订单正常处理: {order_status}")

    def test_tc_fu136_stop_loss_mechanism(self, ctp_service):
        """TC_FU136: 验证止损机制"""
        service = ctp_service
        
        # 先建立持仓
        self._ensure_long_position(service, "IF2512", 1)
        
        # 查询持仓成本
        position_detail = service.query_investor_position(instrument_id="IF2512")
        assert position_detail.get('success'), "持仓明细查询失败"
        
        open_price = 0
        unrealized_pnl = 0
        
        for detail in position_detail.get('data', []):
            if detail.get('InstrumentID') == 'IF2512':
                open_price = detail.get('OpenPrice', 0)
                unrealized_pnl = detail.get('PositionProfit', 0)
                break
        
        print(f"✓ 持仓开仓价: {open_price}, 浮动盈亏: {unrealized_pnl}")
        
        # 设置止损价格（假设亏损5%触发止损）
        stop_loss_price = open_price * 0.95
        
        # 模拟止损条件触发（实际应该由系统自动执行）
        if unrealized_pnl < -open_price * 300 * 0.05:  # 亏损超过5%
            stop_loss_result = service.order_insert(
                instrument_id="IF2512",
                exchange_id="CFFEX",
                direction="卖",
                offset_flag="平仓",
                price=stop_loss_price,
                volume=1
            )
            
            assert stop_loss_result is not None, "止损订单提交失败"
            print(f"✓ 止损机制触发，止损价格: {stop_loss_price}")
        else:
            print(f"✓ 当前未触发止损条件，浮动盈亏: {unrealized_pnl}")

    def test_tc_fu138_margin_call(self, ctp_service):
        """TC_FU138: 验证追加保证金"""
        service = ctp_service
        
        # 查询账户保证金状态
        account_result = service.query_trading_account()
        account_data = account_result.get('data', [{}])[0]
        
        available_fund = account_data.get('Available', 0)
        curr_margin = account_data.get('CurrMargin', 0)
        balance = account_data.get('Balance', 0)
        
        # 计算风险度
        if balance > 0:
            risk_ratio = curr_margin / balance
            print(f"✓ 当前风险度: {risk_ratio:.2%}")
            
            # 模拟保证金不足情况
            if risk_ratio > 0.8:  # 风险度超过80%
                print(f"⚠ 保证金不足，需要追加保证金")
                print(f"  当前可用资金: {available_fund}")
                print(f"  已用保证金: {curr_margin}")
                print(f"  建议追加资金: {curr_margin * 0.2}")
            else:
                print(f"✓ 保证金充足，风险度正常")
        else:
            print(f"⚠ 账户余额异常: {balance}")

    def test_tc_fu139_risk_ratio_calculation(self, ctp_service):
        """TC_FU139: 验证风险度计算"""
        service = ctp_service
        
        # 查询账户信息
        account_result = service.query_trading_account()
        account_data = account_result.get('data', [{}])[0]
        
        balance = account_data.get('Balance', 0)
        curr_margin = account_data.get('CurrMargin', 0)
        available = account_data.get('Available', 0)
        frozen_margin = account_data.get('FrozenMargin', 0)
        
        # 验证资金计算一致性
        calculated_available = balance - curr_margin - frozen_margin
        
        print(f"✓ 账户余额: {balance}")
        print(f"✓ 已用保证金: {curr_margin}")
        print(f"✓ 冻结保证金: {frozen_margin}")
        print(f"✓ 可用资金: {available}")
        print(f"✓ 计算可用资金: {calculated_available}")
        
        # 计算风险度
        if balance > 0:
            risk_ratio = curr_margin / balance
            
            # 风险等级分类
            if risk_ratio < 0.5:
                risk_level = "低风险"
            elif risk_ratio < 0.8:
                risk_level = "中风险"
            else:
                risk_level = "高风险"
            
            print(f"✓ 风险度: {risk_ratio:.2%}, 风险等级: {risk_level}")
            
            # 验证计算准确性
            assert abs(calculated_available - available) < 1.0, "资金计算不一致"
            print(f"✓ 风险度计算准确，资金数据一致")

    @pytest.mark.parametrize("instrument_id,exchange_id,price,volume", [
        ("IF2512", "CFFEX", 4000.0, 1),
        ("rb2512", "SHFE", 3500.0, 5),
        ("cu2501", "SHFE", 75000.0, 1),
    ])
    def test_multiple_contracts_trading(self, ctp_service, instrument_id, exchange_id, price, volume):
        """测试多个合约的交易功能"""
        service = ctp_service
        
        # 查询合约信息
        instrument_result = service.query_instrument(
            instrument_id=instrument_id,
            exchange_id=exchange_id
        )
        
        if not instrument_result.get('success'):
            pytest.skip(f"合约{instrument_id}查询失败，跳过测试")
        
        # 查询账户资金
        account_result = service.query_trading_account()
        account_data_list = account_result.get('data', [])
        account_data = account_data_list[0] if account_data_list else {}
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        
        # 估算保证金需求
        if instrument_id.startswith('IF'):
            estimated_margin = price * 300 * volume * 0.12
        elif instrument_id.startswith('rb'):
            estimated_margin = price * 10 * volume * 0.08
        elif instrument_id.startswith('cu'):
            estimated_margin = price * 5 * volume * 0.08
        else:
            estimated_margin = price * volume * 0.1
        
        if available_fund < estimated_margin:
            pytest.skip(f"资金不足，需要{estimated_margin}，可用{available_fund}")
        
        # 执行开仓
        order_result = service.order_insert(
            instrument_id=instrument_id,
            exchange_id=exchange_id,
            direction="买",
            offset_flag="开仓",
            price=price,
            volume=volume
        )
        
        assert order_result is not None, f"{instrument_id}开仓订单提交失败"
        print(f"✓ {instrument_id}开仓{volume}手订单提交成功")
        
        # 验证结果
        time.sleep(3)
        position_result = service.query_investor_position(instrument_id=instrument_id)
        assert position_result.get('success'), f"{instrument_id}持仓查询失败"
        
        print(f"✓ {instrument_id}交易测试完成")

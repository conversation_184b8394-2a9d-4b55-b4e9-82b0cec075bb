#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度行情数据查询功能测试脚本
测试CTP深度行情数据查询的完整功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from trader.ctp_direct_wrapper import CTPDirectWrapper

def test_depth_market_data_query():
    """测试深度行情数据查询功能"""
    print("=" * 60)
    print("深度行情数据查询功能测试")
    print("=" * 60)
    
    # 创建CTP服务实例
    ctp_service = CTPDirectWrapper()
    
    try:
        # 初始化服务
        print("\n1. 初始化CTP服务...")
        if not ctp_service.initialize():
            print("❌ CTP服务初始化失败")
            return False
        
        print("✅ CTP服务初始化成功")
        
        # 等待连接和登录
        print("\n2. 等待连接和登录...")
        max_wait_time = 30  # 最大等待30秒
        wait_time = 0
        
        while wait_time < max_wait_time:
            if ctp_service.is_logged_in():
                print("✅ 登录成功")
                break
            time.sleep(1)
            wait_time += 1
            if wait_time % 5 == 0:
                print(f"等待登录中... ({wait_time}/{max_wait_time}秒)")
        
        if not ctp_service.is_logged_in():
            print("❌ 登录超时")
            return False
        
        # 测试查询所有深度行情数据
        print("\n3. 测试查询所有深度行情数据...")
        result = ctp_service.query_depth_market_data()
        
        if result["success"]:
            print(f"✅ 查询成功，共获取 {result['count']} 条深度行情数据")
            
            # 显示前几条数据的关键信息
            if result["data"]:
                print("\n前几条深度行情数据概览:")
                for i, data in enumerate(result["data"][:5]):  # 只显示前5条
                    print(f"  {i+1}. 合约: {data.get('InstrumentID', 'N/A')}")
                    print(f"     交易所: {data.get('ExchangeID', 'N/A')}")
                    print(f"     最新价: {data.get('LastPrice', 'N/A')}")
                    print(f"     买一价: {data.get('BidPrice1', 'N/A')}")
                    print(f"     卖一价: {data.get('AskPrice1', 'N/A')}")
                    print(f"     成交量: {data.get('Volume', 'N/A')}")
                    print(f"     更新时间: {data.get('UpdateTime', 'N/A')}")
                    print()
        else:
            print(f"❌ 查询失败: {result.get('error_msg', '未知错误')}")
            return False
        
        # 测试查询指定合约的深度行情数据
        print("\n4. 测试查询指定合约的深度行情数据...")
        
        # 从前面的结果中选择一个合约进行测试
        if result["data"]:
            test_instrument = result["data"][0].get("InstrumentID", "")
            test_exchange = result["data"][0].get("ExchangeID", "")
            
            if test_instrument:
                print(f"查询合约 {test_instrument} 的深度行情数据...")
                specific_result = ctp_service.query_depth_market_data(
                    instrument_id=test_instrument,
                    exchange_id=test_exchange
                )
                
                if specific_result["success"]:
                    print(f"✅ 指定合约查询成功，获取 {specific_result['count']} 条数据")
                    
                    if specific_result["data"]:
                        data = specific_result["data"][0]
                        print(f"合约详细信息:")
                        print(f"  合约代码: {data.get('InstrumentID', 'N/A')}")
                        print(f"  交易所: {data.get('ExchangeID', 'N/A')}")
                        print(f"  交易日: {data.get('TradingDay', 'N/A')}")
                        print(f"  最新价: {data.get('LastPrice', 'N/A')}")
                        print(f"  今开盘: {data.get('OpenPrice', 'N/A')}")
                        print(f"  最高价: {data.get('HighestPrice', 'N/A')}")
                        print(f"  最低价: {data.get('LowestPrice', 'N/A')}")
                        print(f"  昨收盘: {data.get('PreClosePrice', 'N/A')}")
                        print(f"  成交量: {data.get('Volume', 'N/A')}")
                        print(f"  成交金额: {data.get('Turnover', 'N/A')}")
                        print(f"  持仓量: {data.get('OpenInterest', 'N/A')}")
                        print(f"  买一价: {data.get('BidPrice1', 'N/A')}")
                        print(f"  买一量: {data.get('BidVolume1', 'N/A')}")
                        print(f"  卖一价: {data.get('AskPrice1', 'N/A')}")
                        print(f"  卖一量: {data.get('AskVolume1', 'N/A')}")
                        print(f"  更新时间: {data.get('UpdateTime', 'N/A')}")
                        print(f"  更新毫秒: {data.get('UpdateMillisec', 'N/A')}")
                else:
                    print(f"❌ 指定合约查询失败: {specific_result.get('error_msg', '未知错误')}")
                    return False
        
        # 测试查询指定交易所的深度行情数据
        print("\n5. 测试查询指定交易所的深度行情数据...")
        if result["data"]:
            test_exchange = result["data"][0].get("ExchangeID", "")
            if test_exchange:
                print(f"查询交易所 {test_exchange} 的深度行情数据...")
                exchange_result = ctp_service.query_depth_market_data(
                    exchange_id=test_exchange
                )
                
                if exchange_result["success"]:
                    print(f"✅ 指定交易所查询成功，获取 {exchange_result['count']} 条数据")
                else:
                    print(f"❌ 指定交易所查询失败: {exchange_result.get('error_msg', '未知错误')}")
                    return False
        
        print("\n" + "=" * 60)
        print("✅ 深度行情数据查询功能测试完成")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        if ctp_service and hasattr(ctp_service, 'cleanup'):
            ctp_service.cleanup()

def main():
    """主函数"""
    print("深度行情数据查询功能测试")
    print("此测试将验证CTP深度行情数据查询的完整功能")
    
    success = test_depth_market_data_query()
    
    if success:
        print("\n🎉 所有测试通过!")
        return 0
    else:
        print("\n❌ 测试失败!")
        return 1

if __name__ == "__main__":
    exit(main())

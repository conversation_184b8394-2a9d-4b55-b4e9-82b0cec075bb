#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度行情数据查询演示脚本
展示如何使用CTP深度行情数据查询功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from trader.ctp_direct_wrapper import CTPDirectWrapper

def demo_depth_market_data_query():
    """深度行情数据查询演示"""
    print("=" * 80)
    print("CTP深度行情数据查询演示")
    print("=" * 80)
    
    # 创建CTP服务实例
    ctp_service = CTPDirectWrapper()
    
    try:
        # 初始化服务
        print("\n🔧 正在初始化CTP服务...")
        if not ctp_service.initialize():
            print("❌ CTP服务初始化失败")
            return False
        
        print("✅ CTP服务初始化成功")
        
        # 等待连接和登录
        print("\n🔗 正在连接和登录...")
        max_wait_time = 30
        wait_time = 0
        
        while wait_time < max_wait_time:
            if ctp_service.is_logged_in():
                print("✅ 登录成功")
                break
            time.sleep(1)
            wait_time += 1
            if wait_time % 5 == 0:
                print(f"⏳ 等待登录中... ({wait_time}/{max_wait_time}秒)")
        
        if not ctp_service.is_logged_in():
            print("❌ 登录超时")
            return False
        
        # 演示1: 查询所有深度行情数据
        print("\n" + "=" * 50)
        print("📊 演示1: 查询所有深度行情数据")
        print("=" * 50)
        
        result = ctp_service.query_depth_market_data()
        
        if result["success"]:
            print(f"✅ 查询成功，共获取 {result['count']} 条深度行情数据")
            
            if result["data"]:
                print("\n📈 深度行情数据概览 (前10条):")
                print("-" * 120)
                print(f"{'序号':<4} {'合约代码':<12} {'交易所':<8} {'最新价':<10} {'买一价':<10} {'卖一价':<10} {'成交量':<12} {'更新时间':<10}")
                print("-" * 120)
                
                for i, data in enumerate(result["data"][:10]):
                    print(f"{i+1:<4} {data.get('InstrumentID', 'N/A'):<12} {data.get('ExchangeID', 'N/A'):<8} "
                          f"{data.get('LastPrice', 'N/A'):<10} {data.get('BidPrice1', 'N/A'):<10} "
                          f"{data.get('AskPrice1', 'N/A'):<10} {data.get('Volume', 'N/A'):<12} "
                          f"{data.get('UpdateTime', 'N/A'):<10}")
                
                print("-" * 120)
        else:
            print(f"❌ 查询失败: {result.get('error_msg', '未知错误')}")
            return False
        
        # 演示2: 查询指定合约的深度行情数据
        if result["data"]:
            print("\n" + "=" * 50)
            print("🎯 演示2: 查询指定合约的深度行情数据")
            print("=" * 50)
            
            # 选择第一个合约作为示例
            test_instrument = result["data"][0].get("InstrumentID", "")
            test_exchange = result["data"][0].get("ExchangeID", "")
            
            if test_instrument:
                print(f"📋 查询合约: {test_instrument} (交易所: {test_exchange})")
                
                specific_result = ctp_service.query_depth_market_data(
                    instrument_id=test_instrument,
                    exchange_id=test_exchange
                )
                
                if specific_result["success"] and specific_result["data"]:
                    data = specific_result["data"][0]
                    print(f"\n📊 {test_instrument} 详细行情信息:")
                    print("-" * 60)
                    print(f"合约代码:     {data.get('InstrumentID', 'N/A')}")
                    print(f"交易所:       {data.get('ExchangeID', 'N/A')}")
                    print(f"交易日:       {data.get('TradingDay', 'N/A')}")
                    print(f"业务日期:     {data.get('ActionDay', 'N/A')}")
                    print("-" * 60)
                    print(f"最新价:       {data.get('LastPrice', 'N/A')}")
                    print(f"今开盘:       {data.get('OpenPrice', 'N/A')}")
                    print(f"最高价:       {data.get('HighestPrice', 'N/A')}")
                    print(f"最低价:       {data.get('LowestPrice', 'N/A')}")
                    print(f"昨收盘:       {data.get('PreClosePrice', 'N/A')}")
                    print(f"昨结算:       {data.get('PreSettlementPrice', 'N/A')}")
                    print("-" * 60)
                    print(f"成交量:       {data.get('Volume', 'N/A')}")
                    print(f"成交金额:     {data.get('Turnover', 'N/A')}")
                    print(f"持仓量:       {data.get('OpenInterest', 'N/A')}")
                    print(f"昨持仓:       {data.get('PreOpenInterest', 'N/A')}")
                    print("-" * 60)
                    print("五档买卖盘:")
                    for level in range(1, 6):
                        bid_price = data.get(f'BidPrice{level}', 'N/A')
                        bid_volume = data.get(f'BidVolume{level}', 'N/A')
                        ask_price = data.get(f'AskPrice{level}', 'N/A')
                        ask_volume = data.get(f'AskVolume{level}', 'N/A')
                        print(f"  买{level}: {bid_price}({bid_volume})  |  卖{level}: {ask_price}({ask_volume})")
                    print("-" * 60)
                    print(f"涨停价:       {data.get('UpperLimitPrice', 'N/A')}")
                    print(f"跌停价:       {data.get('LowerLimitPrice', 'N/A')}")
                    print(f"当日均价:     {data.get('AveragePrice', 'N/A')}")
                    print(f"更新时间:     {data.get('UpdateTime', 'N/A')}")
                    print(f"更新毫秒:     {data.get('UpdateMillisec', 'N/A')}")
                    print("-" * 60)
                else:
                    print(f"❌ 指定合约查询失败: {specific_result.get('error_msg', '未知错误')}")
        
        # 演示3: 查询指定交易所的深度行情数据
        if result["data"]:
            print("\n" + "=" * 50)
            print("🏢 演示3: 查询指定交易所的深度行情数据")
            print("=" * 50)
            
            # 统计各交易所的合约数量
            exchange_count = {}
            for data in result["data"]:
                exchange = data.get("ExchangeID", "")
                if exchange:
                    exchange_count[exchange] = exchange_count.get(exchange, 0) + 1
            
            print("📊 各交易所深度行情数据统计:")
            print("-" * 30)
            for exchange, count in exchange_count.items():
                print(f"{exchange:<10}: {count:>5} 条")
            print("-" * 30)
            
            # 选择第一个交易所进行详细查询
            if exchange_count:
                test_exchange = list(exchange_count.keys())[0]
                print(f"\n🔍 详细查询交易所 {test_exchange} 的深度行情数据...")
                
                exchange_result = ctp_service.query_depth_market_data(
                    exchange_id=test_exchange
                )
                
                if exchange_result["success"]:
                    print(f"✅ 查询成功，{test_exchange} 交易所共有 {exchange_result['count']} 条深度行情数据")
                    
                    if exchange_result["data"]:
                        print(f"\n📋 {test_exchange} 交易所合约列表 (前10个):")
                        print("-" * 80)
                        print(f"{'序号':<4} {'合约代码':<15} {'最新价':<10} {'成交量':<12} {'更新时间':<10}")
                        print("-" * 80)
                        
                        for i, data in enumerate(exchange_result["data"][:10]):
                            print(f"{i+1:<4} {data.get('InstrumentID', 'N/A'):<15} "
                                  f"{data.get('LastPrice', 'N/A'):<10} {data.get('Volume', 'N/A'):<12} "
                                  f"{data.get('UpdateTime', 'N/A'):<10}")
                        
                        print("-" * 80)
                else:
                    print(f"❌ 交易所查询失败: {exchange_result.get('error_msg', '未知错误')}")
        
        print("\n" + "=" * 80)
        print("🎉 深度行情数据查询演示完成!")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        if ctp_service and hasattr(ctp_service, 'cleanup'):
            ctp_service.cleanup()

def main():
    """主函数"""
    print("CTP深度行情数据查询演示")
    print("此演示将展示如何查询和使用深度行情数据")
    
    success = demo_depth_market_data_query()
    
    if success:
        print("\n✨ 演示成功完成!")
        return 0
    else:
        print("\n💥 演示失败!")
        return 1

if __name__ == "__main__":
    exit(main())
